<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

$student_id = '';
$student_name = '';
$room_info = '';
$payment_history = [];
$total_paid = 0;
$total_unpaid = 0;

if (isset($_GET['id'])) {
    $student_id = data_input($_GET['id']);
    
    // <|im_start|>่ะข้อมูล
    $sql = "SELECT s.*, r.R_number, r.Build 
            FROM student s
            LEFT JOIN room_members rs ON s.Stu_ID = rs.Stu_ID AND rs.is_active = '1'
            LEFT JOIN room r ON rs.Room_ID = r.Room_ID
            WHERE s.Stu_ID = '$student_id'";
    $result = mysqli_query($link, $sql);
    
    if ($result && mysqli_num_rows($result) > 0) {
        $student = mysqli_fetch_assoc($result);
        $student_name = $student['Stu_name'];
        $room_info = "ຕຶກ {$student['Build']} ຫ້ອງ {$student['R_number']}";
        
        // ໃບແຈ້ງໜີ້
        $sql = "SELECT p.*, i.invoice_number, i.period, i.invoice_date, i.due_date, 
                       rc.receipt_number, rc.id as receipt_id
                FROM payments p
                JOIN invoices i ON p.invoice_id = i.id
                JOIN receipts rc ON p.id = rc.payment_id
                WHERE i.Stu_id = '$student_id'
                ORDER BY p.paid_at DESC";
        $result = mysqli_query($link, $sql);
        
        if ($result && mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $payment_history[] = $row;
                $total_paid += $row['amount_paid'];
            }
        }
        
        // ໃບເສັດຮັບເງິນ
        $sql = "SELECT SUM(amount) as total_unpaid
                FROM invoices 
                WHERE Stu_id = '$student_id' AND status = 'unpaid'";
        $result = mysqli_query($link, $sql);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $total_unpaid = $row['total_unpaid'] ?: 0;
        }
    } else {
        $error = "ບໍ່ພົບຂໍ້ມູນນັກສຶກສາ";
    }
}

// ລາຍຊື່ນັກສຶກສາ
$sql = "SELECT s.Stu_ID, s.Stu_name, r.R_number, r.Build
        FROM student s
        LEFT JOIN room_members rs ON s.Stu_ID = rs.Stu_ID AND rs.is_active = '1'
        LEFT JOIN room r ON rs.Room_ID = r.Room_ID
        ORDER BY s.Stu_name ASC";
$students_result = mysqli_query($link, $sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>ປະຫວັດການຊຳລະເງິນຂອງນັກສຶກສາ</title>
       <link rel="icon" href="images/icon_logo.jpg">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <!-- datatable -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <link href="../css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <style>
        .summary-box {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        .summary-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4e73df;
        }
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .paid {
            color: #1cc88a;
        }
        .unpaid {
            color: #e74a3b;
        }
        .print-button {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .card {
                border: none !important;
            }
            .summary-box {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>

<body class="sb-nav-fixed">
    <?php include_once 'menu.php' ?>
    <div id="layoutSidenav_content">
        <main>
            <div class="container-fluid px-4">
                <h1 class="mt-4">ປະຫວັດການຊຳລະເງິນຂອງນັກສຶກສາ</h1>
                <ol class="breadcrumb mb-4 no-print">
                    <li class="breadcrumb-item"><a href="index.php">ໜ້າຫຼັກ</a></li>
                    <li class="breadcrumb-item active">ປະຫວັດການຊຳລະເງິນ</li>
                </ol>
                
                <!-- ຟອມຄົ້ນຫາ -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <i class="fas fa-search"></i> ຄົ້ນຫານັກສຶກສາ
                    </div>
                    <div class="card-body">
                        <form method="get" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="id">ເລືອກນັກສຶກສາ:</label>
                                        <select name="id" id="id" class="form-select" required>
                                            <option value="">--ເລືອກນັກສຶກສາ--</option>
                                            <?php while ($student = mysqli_fetch_assoc($students_result)) { ?>
                                                <option value="<?php echo $student['Stu_ID']; ?>" <?php echo ($student_id == $student['Stu_ID']) ? 'selected' : ''; ?>>
                                                    <?php echo $student['Stu_name']; ?> 
                                                    <?php if (!empty($student['Build']) && !empty($student['R_number'])) { ?>
                                                        - ຕຶກ <?php echo $student['Build']; ?> ຫ້ອງ <?php echo $student['R_number']; ?>
                                                    <?php } ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="submit" class="btn btn-primary form-control">
                                            <i class="fas fa-search"></i> ຄົ້ນຫາ
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if (!empty($student_id) && empty($error)) { ?>
                    <!-- ຂໍ້ມູນນັກສຶກສາ -->
                    <div class="card mb-4">
                        <div class="card-header position-relative">
                            <i class="fas fa-user-graduate"></i> ຂໍ້ມູນນັກສຶກສາ
                            <button onclick="window.print()" class="btn btn-sm btn-secondary print-button no-print">
                                <i class="fas fa-print"></i> ພິມ
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>ຊື່-ນາມສະກຸນ:</strong> <?php echo $student_name; ?></p>
                                    <p><strong>ລະຫັດນັກສຶກສາ:</strong> <?php echo $student_id; ?></p>
                                    <p><strong>ຫ້ອງພັກ:</strong> <?php echo $room_info; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>ເບີໂທ:</strong> <?php echo isset($student['tell']) ? $student['tell'] : '-'; ?></p>
                                    <p><strong>ຜູ້ປົກຄອງ:</strong> <?php echo isset($student['Parent']) ? $student['Parent'] : '-'; ?></p>
                                    <p><strong>ເບີໂທຜູ້ປົກຄອງ:</strong> <?php echo isset($student['Parent_Tell']) ? $student['Parent_Tell'] : '-'; ?></p>
                                </div>
                            </div>
                            
                            <!-- ສະຫຼຸບຂໍ້ມູນການຊຳລະເງິນ -->
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <div class="summary-box">
                                        <div class="summary-title">ຍອດຊຳລະທັງໝົດ</div>
                                        <div class="summary-value paid"><?php echo number_format($total_paid, 2); ?> ກີບ</div>
                                        <div>ຈຳນວນ <?php echo count($payment_history); ?> ລາຍການ</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box">
                                        <div class="summary-title">ຍອດຄ້າງຊຳລະ</div>
                                        <div class="summary-value unpaid"><?php echo number_format($total_unpaid, 2); ?> ກີບ</div>
                                        <div>
                                            <?php if ($total_unpaid > 0) { ?>
                                                <a href="payment.php" class="btn btn-sm btn-danger no-print">
                                                    <i class="fas fa-money-bill-wave"></i> ຊຳລະເງິນ
                                                </a>
                                            <?php } else { ?>
                                                <span class="badge bg-success">ບໍ່ມີໜີ້ຄ້າງຊຳລະ</span>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box">
                                        <div class="summary-title">ຍອດລວມທັງໝົດ</div>
                                        <div class="summary-value"><?php echo number_format($total_paid + $total_unpaid, 2); ?> ກີບ</div>
                                        <div>ຊຳລະແລ້ວ <?php echo ($total_paid > 0) ? number_format(($total_paid / ($total_paid + $total_unpaid)) * 100, 2) : 0; ?>%</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- ຕາຕະລາງປະຫວັດການຊຳລະເງິນ -->
                            <div class="mt-4">
                                <h5>ປະຫວັດການຊຳລະເງິນ</h5>
                                <?php if (count($payment_history) > 0) { ?>
                                    <table id="paymentTable" class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ລຳດັບ</th>
                                                <th>ວັນທີ່ຊຳລະ</th>
                                                <th>ເລກທີ່ໃບແຈ້ງໜີ້</th>
                                                <th>ງວດທີ</th>
                                                <th>ຈຳນວນເງິນ</th>
                                                <th>ວິທີຊຳລະ</th>
                                                <th>ເລກທີ່ໃບເສັດຮັບເງິນ</th>
                                                <th class="no-print">ຈັດການ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $i = 1;
                                            foreach ($payment_history as $payment) { 
                                            ?>
                                                <tr>
                                                    <td><?php echo $i++; ?></td>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($payment['paid_at'])); ?></td>
                                                    <td><?php echo $payment['invoice_number']; ?></td>
                                                    <td><?php echo $payment['period']; ?></td>
                                                    <td class="text-end"><?php echo number_format($payment['amount_paid'], 2); ?> ກີບ</td>
                                                    <td>
                                                        <?php if ($payment['payment_method'] == 'cash') { ?>
                                                            <span class="badge bg-success">ເງິນສົດ</span>
                                                        <?php } else { ?>
                                                            <span class="badge bg-primary">ໂອນເງິນ</span>
                                                        <?php } ?>
                                                    </td>
                                                    <td><?php echo $payment['receipt_number']; ?></td>
                                                    <td class="no-print">
                                                        <a href="receipt-print.php?id=<?php echo $payment['receipt_id']; ?>" class="btn btn-info btn-sm" target="_blank">
                                                            <i class="fas fa-print"></i> ພິມບິນ
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php } ?>
                                        </tbody>
                                    </table>
                                <?php } else { ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> ຍັງບໍ່ມີປະຫວັດການຊຳລະເງິນ
                                    </div>
                                <?php } ?>
                            </div>
                            
                            <!-- ຕາຕະລາງໃບແຈ້ງໜີ້ທີ່ຍັງບໍ່ໄດ້ຊຳລະ -->
                            <?php if ($total_unpaid > 0) { ?>
                                <div class="mt-4">
                                    <h5>ໃບແຈ້ງໜີ້ທີ່ຍັງບໍ່ໄດ້ຊຳລະ</h5>
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ລຳດັບ</th>
                                                <th>ເລກທີ່ໃບແຈ້ງໜີ້</th>
                                                <th>ງວດທີ</th>
                                                <th>ວັນທີ່ອອກໃບແຈ້ງໜີ້</th>
                                                <th>ວັນທີ່ຄົບກຳນົດ</th>
                                                <th>ຈຳນວນເງິນ</th>
                                                <th class="no-print">ຈັດການ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $sql = "SELECT * FROM invoices WHERE Stu_id = '$student_id' AND status = 'unpaid' ORDER BY due_date ASC";
                                            $result = mysqli_query($link, $sql);
                                            $i = 1;
                                            
                                            while ($row = mysqli_fetch_assoc($result)) { 
                                            ?>
                                                <tr>
                                                    <td><?php echo $i++; ?></td>
                                                    <td><?php echo $row['invoice_number']; ?></td>
                                                    <td><?php echo $row['period']; ?></td>
                                                    <td><?php echo date('d/m/Y', strtotime($row['invoice_date'])); ?></td>
                                                    <td>
                                                        <?php 
                                                        $due_date = strtotime($row['due_date']);
                                                        $today = strtotime(date('Y-m-d'));
                                                        $class = ($due_date < $today) ? 'text-danger fw-bold' : '';
                                                        echo "<span class='$class'>" . date('d/m/Y', $due_date) . "</span>";
                                                        ?>
                                                    </td>
                                                    <td class="text-end"><?php echo number_format($row['amount'], 2); ?> ກີບ</td>
                                                    <td class="no-print">
                                                        <a href="payment.php" class="btn btn-success btn-sm">
                                                            <i class="fas fa-money-bill-wave"></i> ຊຳລະເງິນ
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php } ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                <?php } elseif (!empty($error)) { ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php } ?>
            </div>
        </main>
        <?php include_once 'footer.php' ?>
    </div>
    
    <script src="../js/scripts.js"></script>
    <script>
        $(document).ready(function() {
            $('#paymentTable').DataTable({
                language: {
                    url: '../js/la.json'
                },
                order: [[1, 'desc']]
            });
        });
    </script>
</body>
</html>

