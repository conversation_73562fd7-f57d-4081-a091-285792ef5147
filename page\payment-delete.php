<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// ตรวจสอบว่าเป็น admin หรือไม่
if ($_SESSION['role'] != 'admin') {
    echo "ທ່ານບໍ່ມີສິດໃນການລຶບຂໍ້ມູນ";
    exit;
}

// ตรวจสอบว่าเป็น POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo "ວິທີການເຂົ້າເຖິງບໍ່ຖືກຕ້ອງ";
    exit;
}

$userid = $_SESSION['user_id'];

if (isset($_POST['payment_id']) && isset($_POST['payment_number'])) {
    $payment_id = data_input($_POST['payment_id']);
    $payment_number = data_input($_POST['payment_number']);
    
    // ตรวจสอบว่าเป็นตัวเลขหรือไม่
    if (!is_numeric($payment_id) || !is_string($payment_number)) {
        echo "ຂໍ້ມູນບໍ່ຖືກຕ້ອງ";
        exit;
    }
    
    // เริ่ม transaction
    mysqli_begin_transaction($link);
    
    try {
        // 1. ลบข้อมูลการชำระ
        $sql = "DELETE FROM payments WHERE id = ?";
        $stmt = mysqli_prepare($link, $sql);
        mysqli_stmt_bind_param($stmt, "i", $payment_id);
        $result = mysqli_stmt_execute($stmt);
        
        if (!$result) {
            throw new Exception("ຜິດພາດໃນການລຶບຂໍ້ມູນການຊຳລະເງິນ");
        }
        
        // 2. บันทึกลงในตาราง system_logs (ถ้ามี)
        $action = "ລຶບຂໍ້ມູນການຊຳລະເງິນ: ລະຫັດການຊຳລະ $payment_id, ລະຫັດໃບເສັດຮັບເງິນ $payment_number";
        
        // ตรวจสอบว่ามีตาราง system_logs หรือไม่
        $check_table = mysqli_query($link, "SHOW TABLES LIKE 'system_logs'");
        if (mysqli_num_rows($check_table) > 0) {
            $sql = "INSERT INTO system_logs (user_id, action, action_date) VALUES (?, ?, NOW())";
            $stmt = mysqli_prepare($link, $sql);
            mysqli_stmt_bind_param($stmt, "is", $userid, $action);
            mysqli_stmt_execute($stmt);
        }
        
        // Commit transaction
        mysqli_commit($link);
        
        // ส่งผลลัพธ์สำเร็จ (ไม่ส่งข้อความใดๆ เพื่อให้ JavaScript แสดง success message)
        
    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($link);
        echo "ຜິດພາດ: " . $e->getMessage();
    }
} else {
    echo "ຂໍ້ມູນບໍ່ຄົບຖ້ວນ";
}
?>
