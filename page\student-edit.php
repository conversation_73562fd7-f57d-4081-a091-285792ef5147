<?php
// include_once 'login-check.php';
// require_once '../function/function.php';
// require_once 'connect-db.php';

// //ຮັບຄ່າຈາກ URL
// if (isset($_GET['id'])) {
//     $id = data_input($_GET['id']);
//     $sql = "SELECT * FROM student WHERE id='$id'";
//     $result = mysqli_query($link, $sql);
//     $row = mysqli_fetch_assoc($result);
//     $code_stu = $row['code_stu'];
//     $name = $row['name'];
//     $gender = $row['gender'];
//     $date_birth = $row['date_birth'];
//     $major_id = $row['major'];
//     $path_id = $row['path'];
//     $room_id = $row['room_id'];
//     $sets = $row['sets'];
//     $Gen = $row['Gen'];
//     $address = $row['address'];
//     $Parent = $row['parent'];
//     $Parent_Tell = $row['parent_tell'];
//     $tell = $row['tell'];
// }

// // ຮັບຄ່າຈາກຟອມເມື່ອກົດປຸ່ມ btnEdit
// if (isset($_POST['btnEdit'])) {
//     $code_stu = data_input($_POST['code_stu']);
//     $name = data_input($_POST['name']);
//     $gender = $_POST['gender'];
//     $date_birth = $_POST['date_birth'];
//     $major_id = data_input($_POST['major']);
//     $path_id = data_input($_POST['path']);
//     $room_id = data_input($_POST['room_id']);
//     $sets = data_input($_POST['sets']);
//     $Gen = data_input($_POST['Gen']);
//     $address = nl2br(trim(stripslashes($_POST['address'])));
//     $Parent = isset($_POST['parent']) ? data_input($_POST['parent']) : '';
//     $Parent_Tell = isset($_POST['parent_tell']) ? data_input($_POST['parent_tell']) : '';
//     $tell = isset($_POST['tell']) ? data_input($_POST['tell']) : '';

//     $sql = "UPDATE student SET  name = '$name', gender='$gender',tell='$tell', date_birth='$date_birth', address='$address', parent='$Parent', parent_tell='$Parent_Tell', major='$major_id', path='$path_id',sets='$sets',Gen='$Gen', room_id='$room_id'
//     WHERE id='$id'";
//     $result = mysqli_query($link, $sql);
//     if ($result) {
//         $message  = '<script> swal("ສໍາເລັດ","ແກ້ໄຂຂໍ້ມູນແລ້ວ","success",{button: "ຕົກລົງ"}); </script>';
//         $code_stu = $name = $gender = $date_birth = $major_id = $path_id = $room_id = $sets = $Gen = $address = $Parent = $Parent_Tell = $tell = null;
//     } else {
//         echo mysqli_error($link);
//     }
// }

?>
<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

$id = '';
if (isset($_GET['id'])) {
    $id = data_input($_GET['id']);
    $sql = "SELECT * FROM student WHERE id = ?";
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "i", $id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);

    if ($row) {
        $code_stu = $row['code_stu'];
        $name = $row['name'];
        $gender = $row['gender'];
        $date_birth = $row['date_birth'];
        $major_id = $row['major'];
        $path_id = $row['path'];
        $room_id = $row['room_id'];
        $sets = $row['sets'];
        $Gen = $row['Gen'];
        $address = $row['address'];
        $Parent = $row['parent'];
        $Parent_Tell = $row['parent_tell'];
        $tell = $row['tell'];
    }
}

if (isset($_POST['btnEdit'])) {
    $id = data_input($_POST['id']);
    $code_stu = data_input($_POST['code_stu']);
    $name = data_input($_POST['name']);
    $gender = $_POST['gender'];
    $date_birth = $_POST['date_birth'];
    $major_id = data_input($_POST['major']);
    $path_id = data_input($_POST['path']);
    $room_id = data_input($_POST['room_id']);
    $sets = data_input($_POST['sets']);
    $Gen = data_input($_POST['Gen']);
    $address = nl2br(trim(stripslashes($_POST['address'])));
    $Parent = isset($_POST['parent']) ? data_input($_POST['parent']) : '';
    $Parent_Tell = isset($_POST['Parent_Tell']) ? data_input($_POST['Parent_Tell']) : '';
    $tell = isset($_POST['tell']) ? data_input($_POST['tell']) : '';

    $sql = "UPDATE student SET 
        name = ?, gender = ?, tell = ?, date_birth = ?, address = ?, 
        parent = ?, parent_tell = ?, major = ?, path = ?, sets = ?, Gen = ?, room_id = ? 
        WHERE id = ?";

    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "sssssssssssii", 
        $name, $gender, $tell, $date_birth, $address, $Parent, $Parent_Tell, 
        $major_id, $path_id, $sets, $Gen, $room_id, $id
    );

    $result = mysqli_stmt_execute($stmt);

    if ($result) {
        $message  = '<script> swal("ສໍາເລັດ","ແກ້ໄຂຂໍ້ມູນແລ້ວ","success",{button: "ຕົກລົງ"}).then(function() {
            window.location.href = "student-management.php?update=success";
        }); </script>';
        $code_stu = $name = $gender = $date_birth = $major_id = $path_id = $room_id = $sets = $Gen = $address = $Parent = $Parent_Tell = $tell = null;
    } else {
        echo mysqli_error($link);
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />

    <title>ລະບົບຈັດການຂໍ້ມູນນັກສຶກສາ</title>
    <link rel="icon" href="../images/3.png">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <!-- price format -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.priceformat.min.js"></script>

    <!-- ສໍາລັບຮູບ -->
    <style>
        .btn-file {
            position: relative;
            overflow: hidden;
        }

        .btn-file input[type=file] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            background: white;
            cursor: inherit;
            display: block;
        }

        #img-upload {
            width: 150px;
            height: 185px;
            margin-bottom: 20px;
        }
    </style>
</head>

</head>

<body class="sb-nav-fixed">
    <!-- ສະແດງ message ແຈ້ງເຕືອນ -->
    <?= @$message ?>

    <!-- ດຶງເມນູເຂົ້າມາ  -->
    <?php
    // include_once 'menu.php' 
    ?>

    <div id="layoutSidenav_content">
        <main>
            <div class="container-fluid px-4">

                <span class="d-flex justify-content-end mt-2">
                    <a href="student-management.php" class="btn btn-secondary"> <i class="fas fa-address-card"></i>&nbsp;ສະແດງຂໍ້ມູນ</a>
                </span>
                <div class="card border-primary">
                    <div class="card-header bg-info text-white h5">ຟອມປ້ອນຂໍ້ມູນນັກສຶກສາ</div>
                    <div class="card-body">
                      <form action="<?= htmlspecialchars($_SERVER['PHP_SELF'], ENT_QUOTES, 'UTF-8') ?>" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="id" value="<?= @$id ?>">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <!--ລະຫັດພະນັກງານ-->
                                            <div class="mb-3">
                                                <label for="code_stu" class="form-label">ລະຫັດນັກສຶກສາ:</label>
                                                <input type="text" class="form-control" id="code_stu" name="code_stu" value="<?= @$code_stu ?>" readonly>

                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!--ຊື່ພະນັກງານ-->
                                            <div class="mb-3">
                                                <label for="name" class="form-label">ຊື່ນັກງານ:</label>
                                                <input type="text" class="form-control" id="name" placeholder="ກະລຸນາປ້ອນຊື່ ແລະ ນາມສະກຸນ" name="name" value="<?= @$name ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!--ເພດ-->
                                            <fieldset class="mb-3">
                                                <p>ເພດ</p>
                                                <div class="form-check-inline">
                                                    <input type="radio" class="form-check-input" id="gender1" name="gender" value="M" <?php if (@$gender == '' || @$gender == 'M') echo 'checked'; ?>>
                                                    <label class="form-check-label" for="gender1">ຊາຍ</label>
                                                </div>
                                                <div class="form-check-inline">
                                                    <input type="radio" class="form-check-input" id="gender2" name="gender" value="F" <?php if (@$gender == 'F') echo 'checked'; ?>>
                                                    <label class="form-check-label" for="gender2">ຍິງ</label>
                                                </div>
                                            </fieldset>
                                        </div>
                                        <div class="col-md-6">
                                            <!--ວັນເດືອນປີເກີດ-->
                                            <div class="mb-3">
                                                <label for="date_birth" class="form-label">ວັນ, ເດືອນ ປີເກີດ:</label>
                                                <input type="date" class="form-control" id="date_birth" name="date_birth" value="<?= @$date_birth ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <!--ທີ່ຢູ່-->
                                            <div class="mb-3">
                                                <label for="address">ທີ່ຢູ່:</label>
                                                <textarea class="form-control" rows="5" id="address" name="address"><?= @strip_tags($address) ?></textarea>
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <!--ສາຂາ-->
                                            <div class="mb-3">
                                                <label for="major" class="form-label">ສາຂາ:</label>
                                                <select class="form-select" id="major" name="major" required>
                                                    <option value="">----ເລືອກສາຂາ-----</option>
                                                    <?php
                                                    $sql = "SELECT id, name FROM majors ORDER  BY name ASC";
                                                    $result = mysqli_query($link, $sql);
                                                    while ($row = mysqli_fetch_assoc($result)) {
                                                    ?>
                                                        <option value="<?= @$row['id'] ?>" <?php if (@$major_id == @$row['id']) echo 'selected' ?>>
                                                            <?= @$row['name'] ?>
                                                        </option>
                                                    <?php
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">


                                            <div class="mb-3">
                                                <label for="Gen">ຮຸ້ນທີ:</label>
                                               <input type="text" class="form-control" id="Gen" name="Gen" value="<?= @$Gen ?>">
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <!--ພາກຮຽນ-->
                                            <div class="mb-3">
                                                <label for="path" class="form-label">ພາກຮຽນ:</label>
                                                <select class="form-select" id="path" name="path" required>
                                                    <option value="">----ເລືອກພາກຮຽນ-----</option>
                                                    <?php
                                                    $sql = "SELECT id, path FROM path ORDER  BY path ASC";
                                                    $result = mysqli_query($link, $sql);
                                                    while ($row = mysqli_fetch_assoc($result)) {
                                                    ?>
                                                        <option value="<?= @$row['id'] ?>" <?php if (@$path_id == @$row['id']) echo 'selected' ?>>
                                                            <?= @$row['path'] ?>
                                                        </option>
                                                    <?php
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="room_id" class="form-label">ຫ້ອງພັກ:</label>
                                                <select class="form-select" id="room_id" name="room_id" required>
                                                    <option value="">----ເລືອກຫ້ອງພັກ-----</option>
                                                    <?php
                                                    $sql = "SELECT * FROM room ORDER BY number ASC";
                                                    $result = mysqli_query($link, $sql);
                                                    while ($row = mysqli_fetch_assoc($result)) {
                                                    ?>
                                                        <option value="<?php echo $row['id'] ?>" <?php if (@$room_id == $row['id']) echo 'selected'; ?>>
                                                            <?php echo $row['number'] ?>
                                                        </option>
                                                    <?php } ?>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-6">

                                            <div class="mb-3">
                                                <label for="sets">ຊຸດຮຽນ:</label>
                                                <input type="text" class="form-control" id="sets" placeholder="ປ້ອນຊຸດຮຽນ" name="sets" value="<?= @$sets ?>">
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <!--ເງິນອຸດໜູນ-->
                                            <div class="mb-3">
                                                <label for="Parent" class="form-label">ຜູ້ປົກຄອງ</label>
                                                <input type="text" class="form-control" id="Parent" placeholder="ປ້ອນຊື່ຜູ້ປົກຄອງ" name="Parent" value="<?= @$Parent ?>">
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <!--ເງິນອຸດໜູນ-->
                                            <div class="mb-3">
                                                <label for="tell" class="form-label">ເບີໂທລະສັບນັກສຶກສາ</label>
                                                <input type="text" class="form-control" id="tell" placeholder="ປ້ອນເບີໂທລະສັບນັກສຶກສາ" name="tell" value="<?= @$tell ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!--ເງິນອຸດໜູນ-->
                                            <div class="mb-3">
                                                <label for="Parent_Tell	" class="form-label">ເບີໂທລະສັບຜູ້ປົກຄອງ</label>
                                                <input type="text" class="form-control" id="Parent_Tell" name="Parent_Tell" value="<?= @$Parent_Tell ?>">
                                            </div>
                                        </div>


                                    </div>
                                </div>


                                <div class="col-md-12 text-center">
                                    <!--ປຸ່ມ-->
                                    <input type="submit" name="btnEdit" value="ແກ້ໄຂຂໍ້ມູນ" class="btn btn-primary" style="width: 100px; border-radius: 20px">
                                    &nbsp;&nbsp;
                                    <a href="student-management.php" class="btn btn-warning" style="width: 100px; border-radius: 20px">ຍົກເລີກ</a>

                                </div>

                            </div>
                    </div>
                </div>



            </div>

            </form>
    </div>
    </div>

    </div>
    </main>
    <!-- footer -->
    <?php include_once 'footer.php' ?>

    </div>


</body>

</html>




<script>
    $(document).ready(function() {


        $('.btn-file :file').on('fileselect', function(event, label) {

            var input = $(this).parents('.input-group').find(':text'),
                log = label;

            if (input.length) {
                input.val(log);
            } else {
                if (log)
                    alert(log);
            }

        });

        function readURL(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#img-upload').attr('src', e.target.result);
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        $("#imgInp").change(function() {
            readURL(this);
        });
        /*ສິ້ນສຸດເລືອກຮູບ*/

        /*ແຍກຈຸດຫຼັກພັນ ....*/
        $('#incentive').priceFormat({
            prefix: '',
            thousandsSeparator: '.',
            centsLimit: 0
        });
    });

    /* ບໍ່ໃຫ້ມັນຊັບມິດຄືນ*/
    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }
</script>