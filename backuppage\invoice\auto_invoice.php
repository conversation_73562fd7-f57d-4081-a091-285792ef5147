<?php
include '../connect-db.php';

function generateInvoiceNumber() {
    return 'INV-'.date('Ymd').'-'.rand(1000,9999);
}

// Core logic moved into function
function generateNextInvoices() {
    global $link;
    $today = date('Y-m-d');
    $months_per_period = 5;
    $output = '';
    
  
        $sql = "
    SELECT i.Stu_id, i.room_id, i.invoice_date
    FROM invoices i
    JOIN room_members rm ON rm.Stu_id=i.Stu_id AND rm.room_id=i.room_id
    WHERE i.status='paid'
      AND rm.is_active=1
      AND YEAR(i.invoice_date)=YEAR(CURDATE())
      AND i.invoice_date=(SELECT MAX(ii.invoice_date)
                          FROM invoices ii
                          WHERE ii.Stu_id=i.Stu_id
                            AND ii.room_id=i.room_id
                            AND ii.status='paid'
                            AND YEAR(ii.invoice_date)=YEAR(i.invoice_date))
    ";
    $res = mysqli_query($link, $sql);
    while ($row = mysqli_fetch_assoc($res)) {
        $stu = $row['Stu_id'];
        $room = $row['room_id'];
        // $inv_dt = $row['invoice_date'];
        $last_paid_date = $row['invoice_date'];
        // next invoice date
         $next_inv_date = date('Y-m-01', strtotime("first day of + {$months_per_period} months", strtotime($last_paid_date)));
        $gen_date = date('Y-m-d', strtotime("-7 days", strtotime($next_inv_date)));

        if ($today >= $gen_date) {
            $month = (int)date('m', strtotime($next_inv_date));
            if ($month <=4) $period=1; elseif($month<=8) $period=2; else $period=3;
            // check exists
            $chk = mysqli_query($link, "SELECT COUNT(*) FROM invoices WHERE Stu_id='$stu' AND room_id='$room' AND period='$period' AND YEAR(invoice_date)=YEAR('$next_inv_date')");
            if (mysqli_fetch_row($chk)[0]==0) {
                // price
                $pr = mysqli_query($link, "SELECT Price FROM room WHERE Room_ID='$room'");
                $price = mysqli_fetch_assoc($pr)['Price'];
                $due_dt = date('Y-m-d', strtotime('+7 days', strtotime($next_inv_date)));
                // $inv_id = autoID('invoices');
                $inv_num = generateInvoiceNumber();
                $insert = mysqli_query($link, "INSERT INTO invoices (Stu_id,room_id,invoice_number,invoice_date,due_date,amount,status,period,note) VALUES
                    ('$stu','$room','$inv_num','$next_inv_date','$due_dt','$price','unpaid','$period','ລະບົບສ້າງ')");
                if ($insert) {
                    $output .= "Created invoice for $stu room $room date $next_inv_date (period $period)<br>";
                } else {
                    $output .= "Error: " . mysqli_error($link) . "<br>";
                }
            } else {
                $output .= "Invoice already exists for $stu room $room period $period<br>";
            }
        } else {
            $output .= "Not time yet for $stu room $room (next invoice $next_inv_date)<br>";
        }
    }
    return $output;
}

// If form submitted
// $message = '';
// if ($_SERVER['REQUEST_METHOD']==='POST') {
//   $message = generateNextInvoices();
// }
?>
<!-- <!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Test Generate Next Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-4">
<div class="container">
    <h3>Run Generate Next Invoice Now</h3>
    <form method="post">
        <button type="submit" class="btn btn-primary">Run Now</button>
    </form>
    <div class="mt-4"> -->
        <?php 
                // echo $message;
        ?>
    <!-- </div>
</div>
</body> -->
<!-- </html> -->
<?php
// echo "auto_invoice.php is working!";
// // หรือ
// var_dump("auto_invoice.php is loaded");
?>