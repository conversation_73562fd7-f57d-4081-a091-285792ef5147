<?php
function data_input($data){
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
  }
  // ສ້າງເລກທີ່ຈ່າຍເງິນ
 function generatepaymentNumber()
{
    global $link;
    $datePart = date('Ymd');
    $prefix   = "RCE-" . $datePart;

    $sql    = "SELECT MAX(payment_number) AS payment_number FROM payments WHERE payment_number LIKE '$prefix%'";
    $result = mysqli_query($link, $sql);
    $row    = mysqli_fetch_assoc($result);

    if (empty($row['payment_number'])) {
        $payment_number = $prefix . "-001";
    } else {
        $lastId         = substr($row['payment_number'], -3); // ດຶງ 3 ຕົວສຸດທ້າຍ
        $newId          = str_pad((int) $lastId + 1, 3, "0", STR_PAD_LEFT);
        $payment_number = $prefix . "-" . $newId;
    }
    return $payment_number;
}