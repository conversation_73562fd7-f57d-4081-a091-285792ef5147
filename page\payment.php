<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// คืนค่าจำนวนเงินที่ต้องชำระในงวดถัดไป
function getAmountForNextPeriod($invoice_id) {
    global $link;
    $sql = "SELECT Price FROM room WHERE Room_ID = (SELECT room_id FROM invoices WHERE id = '$invoice_id')";
    $result = mysqli_query($link, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['Price'];
}
//ສ້າງຟັງຊັນເພື່ອ ສ້າງລະຫັດອັດຕະໂນມັດ
function autoID(){
    global $link;
    $datePart = date('Ymd'); // ຮູບແບບ: 20250625
    $prefix = "REC"."-" . $datePart;

    $sql = "SELECT MAX(receipt_number) AS receipt_number FROM receipts WHERE receipt_number LIKE '$prefix%'";
    $result = mysqli_query($link, $sql);
    $row = mysqli_fetch_assoc($result);

    if(empty($row['receipt_number'])) {
        $receipt_number = $prefix . "-001";
    } else {
        $lastId = substr($row['receipt_number'], -3); // ດຶງ 3 ຕົວສຸດທ້າຍ
        $newId = str_pad((int)$lastId + 1, 3, "0", STR_PAD_LEFT);
        $receipt_number = $prefix . "-" . $newId;
    }
    return $receipt_number;
}

autoID(); //ເອີ້ນໃຊ້ autoID   
// เมื่อกดปุ่มชำระเงิน
if (isset($_POST['btnPay'])) {
    $invoice_id = data_input($_POST['invoice_id']);
    $amount_paid = str_replace(',', '', $_POST['amount_paid']);
    $payment_method = data_input($_POST['payment_method']);

    // ตรวจสอบจำนวนเงิน
    $sql = "SELECT amount FROM invoices WHERE id = '$invoice_id'";
    $result = mysqli_query($link, $sql);
    $row = mysqli_fetch_assoc($result);

    if ($amount_paid < $row['amount']) {
        $error_amount = "ຈຳນວນເງິນບໍ່ພຽງພໍ";
    } else {
        mysqli_begin_transaction($link);

        try {
            // 1. เพิ่ม payment
            $sql = "INSERT INTO payments (invoice_id, paid_at, amount_paid, payment_method) 
                    VALUES ('$invoice_id', NOW(), '$amount_paid', '$payment_method')";
            $result = mysqli_query($link, $sql);
            if (!$result) throw new Exception(mysqli_error($link));

            $payment_id = mysqli_insert_id($link); // ✅ ดึง payment_id

            // 2. อัพเดท invoice
            $sql = "UPDATE invoices SET status = 'paid' WHERE id = '$invoice_id'";
            $result = mysqli_query($link, $sql);
            if (!$result) throw new Exception(mysqli_error($link));

            // 3. เพิ่ม receipt
            $sql = "INSERT INTO receipts (payment_id, receipt_number, issued_at) 
                    VALUES ('$payment_id', '". autoID() ."', NOW())";
            $result = mysqli_query($link, $sql);
            if (!$result) throw new Exception(mysqli_error($link));

            $receipt_id = mysqli_insert_id($link); // ✅ ดึง receipt_id

            mysqli_commit($link);

            $message = '<script>
                swal("ສຳເລັດ", "ຊຳລະເງິນສຳເລັດ", "success", {button: "ຕົກລົງ"}).then(function() {
                    window.location = "receipt-print.php?id=' . $payment_id . '";
                });
            </script>';
        } catch (Exception $e) {
            mysqli_rollback($link);
            echo "ຜິດພາດ: " . $e->getMessage();
        }
    }
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ລະບົບຈັດການຫໍພັກ - ຊຳລະເງິນ</title>

       <link rel="icon" href="../images/3.png">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <!-- datatable -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">
    <style>
    .ts-dropdown .ts-dropdown-content {
        max-height: 200px;
        /* ความสูงสูงสุดของรายการ */
        overflow-y: auto;
        /* แสดง scrollbar เมื่อเกิน */
    }
    </style>

</head>

<body class="sb-nav-fixed">
    <?php include_once 'menu.php' ?>
    <div id="layoutSidenav_content">
        <div class="container-fluid mt-2">
            <div class="row">
                <div class="col-md-4 mt-4">
                    <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">ຊຳລະເງິນ</legend>
                        <form method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="invoice_id">ຂໍ້ມູນນັກສຶກສາ</label>
                                <select name="invoice_id" id="select-room" class="form-select" required>
                                    <option value="">--ເລຶອກ--</option>
                                    <?php
                                $sql = "SELECT i.id, i.invoice_date, i.amount, i.due_date, s.Stu_name, r.R_number, r.Build
                                        FROM invoices i
                                        JOIN student s ON i.Stu_id = s.Stu_ID
                                        JOIN room r ON i.room_id = r.Room_ID
                                        WHERE i.status = 'unpaid'
                                        ORDER BY i.due_date ASC";
                                $result = mysqli_query($link, $sql);
                                while ($row = mysqli_fetch_assoc($result)) {
                                    echo "<option value='{$row['id']}'>
                                        {$row['Stu_name']} - ຕຶກ {$row['Build']} ຫ້ອງ {$row['R_number']} - 
                                      {$row['amount']} ກີບ (ຄົບກຳໜົດ: {$row['due_date']})
                                    </option>";
                                }
                                ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="amount_paid">ຈຳນວນເງິນທີ່ຈະຊຳລະ</label>
                                <input type="text" name="amount_paid" id="amount_paid"
                                    class="form-control number-format" required>

                                <div class="text-danger"><?= @$error_amount ?></div>
                            </div>

                            <div class="form-group">
                                <label for="payment_method">ວິທີຊຳລະ</label>
                                <select name="payment_method" id="payment_method" class="form-select" required>
                                    <option value="cash">ເງິນສົດ</option>
                                    <option value="bank_transfer">ໂອນເງິນ</option>
                                </select>
                            </div>

                            <button type="submit" name="btnPay" class="btn btn-primary btn-block"
                                style="margin-top: 20px;">ບັນທຶກ</button>
                        </form>
                    </fieldset>
                </div>

                <div class="col-md-8 mt-4">
                    <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">ປະຫວັດການຊຳລະເງິນ</legend>
                        <table id="example" class="table table-striped" style="width:100%">
                            <thead class="bg-secondary text-center text-white">
                                <tr>
                                    <th>ລຳດັບ</th>
                                    <th>ວັນທີຊຳລະ</th>
                                    <th>ນັກສຶກສາ</th>
                                    <th>ຫ້ອງພັກ</th>
                                    <th>ຈຳນວນເງິນ</th>
                                    <th>ວີທີຊຳລະ</th>
                                    <th>ເລກທີບິນ</th>
                                    <th>ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                            $sql = "SELECT p.*, i.Stu_id, i.room_id, s.Stu_name, r.R_number, r.Build, rc.receipt_number, rc.id as receipt_id, p.id as payment_id
                                    FROM payments p
                                    JOIN invoices i ON p.invoice_id = i.id
                                    JOIN student s ON i.Stu_id = s.Stu_ID
                                    JOIN room r ON i.room_id = r.Room_ID
                                    JOIN receipts rc ON p.id = rc.payment_id
                                    ORDER BY p.paid_at DESC";
                            $result = mysqli_query($link, $sql);
                            $number = 1;
                            while ($row = mysqli_fetch_assoc($result)) {
                            ?>
                                <tr>
                                    <td class="text-center"><?= $number++ ?></td>
                                    <td><?= date('d/m/Y H:i', strtotime($row['paid_at'])) ?></td>
                                    <td><?= $row['Stu_name'] ?></td>
                                    <td>ຕຶກ <?= $row['Build'] ?> ຫ້ອງ <?= $row['R_number'] ?></td>
                                    <td class="text-right"><?= number_format($row['amount_paid'], 2) ?> ກີບ</td>
                                    <td><?= $row['payment_method'] == 'cash' ? 'ເງິນສົດ' : 'ໂອນເງິນ' ?></td>
                                    <td><?= $row['receipt_number'] ?></td>
                                    <td class="text-center">
                                        <a href="receipt-print.php?id=<?= $row['payment_id'] ?>"
                                            class="btn btn-info btn-sm">
                                            <i class="fas fa-print"></i> ພິມບິນ
                                        </a>
                                        <?php if ($_SESSION['role'] == 'admin') { ?>
                                        <button type="button" class="btn btn-danger btn-sm" 
                                            onclick="deletePayment('<?= $row['payment_id'] ?>', '<?= $row['receipt_id'] ?>')">
                                            <i class="fas fa-trash-alt"></i> ລຶບ
                                        </button>
                                        <?php } ?>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </fieldset>
                </div>
            </div>
        </div>

        <?php
    if (isset($message)) {
        echo $message;
    }
    ?>
        <?php include_once 'footer.php' ?>
    </div>
</body>
<script>
$(document).ready(function() {
    $('.number-format').priceFormat({
        prefix: '',
        centsSeparator: '.',
        thousandsSeparator: ',',
        centsLimit: 2
    });
    
    // เışı่ม DataTable
    $('#example').DataTable({
        "language": {
            "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
            "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
            "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
            "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
            "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
            "search": "ຄົ້ນຫາ:",
            "paginate": {
                "first": "ໜ້າທຳອິດ",
                "last": "ໜ້າສຸດທ້າຍ",
                "next": "ໜ້າຕໍ່ໄປ",
                "previous": "ໜ້າກ່ອນ"
            }
        }
    });
});

// ຟັງຊັນສຳລັບລຶບຂໍ້ມູນການຊຳລະເງິນ
function deletePayment(paymentId, receiptId) {
    swal({
        title: "ເຈົ້າຕ້ອງການລຶບແທ້ ຫຼື ບໍ່?",
        text: "ເມື່ອລຶບແລ້ວຈະບໍ່ສາມາດກູ້ຄືນໄດ້!",
        icon: "warning",
        buttons: true,
        dangerMode: true,
        buttons: ['ຍົກເລີກ', 'ຕົກລົງ']
    })
    .then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: "payment-delete.php",
                method: "post",
                data: {
                    payment_id: paymentId,
                    receipt_id: receiptId
                },
                success: function(data) {
                    if (data) {
                        alert(data);
                    } else {
                        swal("ສໍາເລັດ", "ຂໍ້ມູນຖືກລຶບອອກຈາກຖານຂໍ້ມູນແລ້ວ", "success", {
                            button: "ຕົກລົງ",
                        });
                        setTimeout(function() {
                            location.reload();
                        }, 1000); 
                    }
                }
            });
        } else {
            swal("ຂໍ້ມູນຂອງທ່ານຍັງປອດໄພ!", {
                button: "ຕົກລົງ",
            });
        }
    });
}



</script>
<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script>
new TomSelect("#select-room", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກສາ"
});
</script>

</html>

