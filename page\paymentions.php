<?php error_reporting(E_ALL); ?>
<?php
ini_set('display_errors', 1);
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';
// Initialize variables
$room_price = 0;
//ດຶງຂໍ້ມູນຈ່າຍເງິນ
$sql = "SELECT p.*, s.Stu_name, r.R_number
            FROM payments p
            JOIN student s ON p.Stu_id = s.Stu_ID
            JOIN room r ON p.room_id = r.Room_ID
            ORDER BY p.payment_date DESC";
$result       = mysqli_query($link, $sql);
$payment_data = [];
if ($result) {
    $payment_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
}
// ສ້າງເລກທີ່ຈ່າຍເງິນ
function generatepaymentNumber()
{
    global $link;
    $datePart = date('Ymd');
    $prefix   = "RCE-" . $datePart;

    $sql    = "SELECT MAX(payment_number) AS payment_number FROM payments WHERE payment_number LIKE '$prefix%'";
    $result = mysqli_query($link, $sql);
    $row    = mysqli_fetch_assoc($result);

    if (empty($row['payment_number'])) {
        $payment_number = $prefix . "-001";
    } else {
        $lastId         = substr($row['payment_number'], -3); // ດຶງ 3 ຕົວສຸດທ້າຍ
        $newId          = str_pad((int) $lastId + 1, 3, "0", STR_PAD_LEFT);
        $payment_number = $prefix . "-" . $newId;
    }
    return $payment_number;
}

//ບັນທຶກການຈ່າຍເງິນ
if (isset($_POST['btnPay'])) {
    $Stu_id         = data_input($_POST['Stu_id']);
    $qty            = data_input($_POST['qty']);
    $payment_method = data_input($_POST['payment_method']);
    $note           = data_input($_POST['note']);

    //ດຶງລະຫັດຫ້ອງຈາກລະຫັດນັກສຶກສາ
    $sql_room_id  = "SELECT room_id FROM room_members WHERE Stu_id = ?";
    $stmt_room_id = mysqli_prepare($link, $sql_room_id);
    mysqli_stmt_bind_param($stmt_room_id, "s", $Stu_id);
    mysqli_stmt_execute($stmt_room_id);
    $result_room_id = mysqli_stmt_get_result($stmt_room_id);
    $row_room_id    = mysqli_fetch_assoc($result_room_id);
    $room_id        = $row_room_id['room_id'];

    // ດຶງລາຄາຫ້ອງທີ່ເລືອກຈ່າຍເງິນ
    $sql_room  = "SELECT Price FROM room WHERE Room_ID = ?";
    $stmt_room = mysqli_prepare($link, $sql_room);
    mysqli_stmt_bind_param($stmt_room, "s", $room_id);
    mysqli_stmt_execute($stmt_room);
    $result_room = mysqli_stmt_get_result($stmt_room);
    $room_data   = mysqli_fetch_assoc($result_room);

    if ($room_data) {
        $room_price = $room_data['Price'];
        if ($qty == 4) {
            $num = 1;
        } else if ($qty == 8) {
            $num = 2;
        } else if ($qty == 12) {
            $num = 3;
        }
        // ລວມຈຳນວນເງິນ
        $total_amount = $num * $room_price;
    } else {
        $error_message = "ບໍ່ພົບຂໍ້ມູນຫ້ອງ";
    }

    if (! isset($error_message)) {
        // ສ້າງເລກທີ່ຈ່າຍເງິນ
        $payment_number = generatepaymentNumber();
        // ລົງລະຫັດໃບເສັດຮັບເງິນ
        $sql = "INSERT INTO payments (Stu_id, room_id, payment_number, payment_date, next_payment_date, amount, qty, payment_method, note)
                    VALUES (?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL ? MONTH), ?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $sql);
        mysqli_stmt_bind_param($stmt, "sssissis", $Stu_id, $room_id, $payment_number, $qty, $total_amount, $qty, $payment_method, $note);
        $result = mysqli_stmt_execute($stmt);

        if ($result) {
            $message = '<script>swal("ສຳເລັດ", "ຊຳລະເງິນສຳເລັດ", "success",{button: "ຕົກລົງ"}).then(function() {
                window.location = "paymentions-print.php?id=" . mysqli_insert_id($link);
            });</script>';
        } else {
            $error_message = "ຜິດພາດໃນການບັນທຶກຂໍ້ມູນ: " . mysqli_error($link);
        }
    }
    $Stu_id         = $qty         = $payment_method         = $note         = null;
    $room_price     = 0;
    $total_amount   = 0;
    $payment_number = null;
    $payment_data   = null;
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paymentions || ຈ່າຍເງິນ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.complete.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap5.css">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">

    <style>
        .ts-dropdown .ts-dropdown-content {
            max-height: 80px;
            overflow-y: auto;
        }
    </style>

</head>

<body>
    <div class="sb-nav-fixed">
        <?php include_once 'menu.php' ?>
        <?php echo @$message ?>
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        <div id="layoutSidenav_content">
            <div class="container-fluid mt-2">
                <div class="row">
                    <div class="col-md-4 mt-4">
                        <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                            <legend class="float-none w-auto p-2 h5">ຈ່າຍເງິນ</legend>
                            <form method="post" enctype="multipart/form-data">
                                <div class="col-md-12">
                                    <label for="Stu_id">ນັກສຶກສາ</label>
                                    <select name="Stu_id" id="Stu_id" class="form-select" required>
                                        <option value="">--ເລືອກ--</option>
                                        <?php
                                        $sql    = "SELECT Stu_ID, Stu_name FROM student WHERE status != 'ອອກພັກແລ້ວ'";
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            echo "<option value='{$row['Stu_ID']}'>{$row['Stu_name']}</option>";
                                        }
                                        ?>
                                    </select>
                                    <div class="text-danger"><?php echo @$error_Stu_id ?></div>
                                </div>

                                <div class="col-md-12">
                                    <label for="qty">ຈຳນວນເດືອນ</label>
                                    <input type="number" name="qty" id="qty" class="form-control" min="4" max="12" step="4" required>
                                </div>

                                <div class="col-12">
                                    <label for="payment_method">ວິທີຊຳລະ</label>
                                    <select name="payment_method" id="payment_method" class="form-select" required>
                                        <option value="1">ເງິນສົດ</option>
                                        <option value="0">ໂອນເງິນ</option>
                                    </select>

                                </div>
                                <div class="col-md-12">
                                    <label for="note">ໝາຍເຫດ</label>
                                    <textarea name="note" id="note" class="form-control"></textarea>
                                </div><br>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" name="btnPay">ຈ່າຍເງິນ</button>
                                </div>

                            </form>
                        </fieldset>
                    </div>
                    <div class="col">
                        <h3>ປະຫວັດການຈ່າຍເງິນ</h3>
                        <table class="table table-striped-columns table-hover table-bordered border-black" id="example">
                            <thead>
                                <tr class="table-primary">
                                    <th>ລຳດັບ</th>
                                    <th>ຊື່</th>
                                    <th>ຫ້ອງ</th>
                                    <th>ຈຳນວນເງິນ</th>
                                    <th>ວັນທີ່ຊຳລະ</th>
                                    <th>ວັນທີ່ຕ້ອງຈ່າຍຄັ້ງຕໍ່ໄປ</th>
                                    <th>ເລກທີ່ການຊຳລະ</th>
                                    <th>ວິທີຊຳລະ</th>
                                    <th>ໝາຍເຫດ</th>
                                    <th>ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (! empty($payment_data)): ?>
                                    <?php foreach ($payment_data as $i => $rows): ?>
                                        <tr>
                                            <td><?php echo $i + 1 ?></td>
                                            <td><?php echo $rows['Stu_name'] ?></td>
                                            <td><?php echo $rows['R_number'] ?></td>
                                            <td><?php echo number_format($rows['amount'], 2) ?> ກີບ</td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($rows['payment_date'])) ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($rows['next_payment_date'])) ?></td>
                                            <td><?php echo $rows['payment_number'] ?></td>
                                            <td><?php echo $rows['payment_method'] == 1 ? 'ເງິນສົດ' : 'ໂອນເງິນ' ?></td>
                                            <td><?php echo $rows['note'] ?></td>
                                            <td>
                                                <a href="paymentions-print.php?id=<?php echo $rows['id'] ?>" class="btn btn-info btn-sm" target="_blank">
                                                    <i class="fas fa-print"></i> ພິມບິນ
                                                </a>
                                                <?php if ($_SESSION['role'] == 'admin'): ?>
                                                    <a href="#" onclick="deletePayment('<?php echo $rows['id'] ?>', '<?php echo $rows['payment_number'] ?>')" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash-alt"></i> ລຶບ
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="text-center">ບໍ່ມີຂໍ້ມູນການຊຳລະເງິນ</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
</body>
<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script>
    new TomSelect("#Stu_id", {
        create: false,
        sortField: {
            field: "text",
            direction: "asc"
        },
        placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກ",
        maxItems: 1
    });
</script>
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap5.js"></script>
<script>
    $(document).ready(function() {
        $('#example').DataTable({
            "language": {
                "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
                "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
                "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
                "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
                "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
                "search": "ຄົ້ນຫາ:",
                "paginate": {
                    "first": "ໜ້າທຳອິດ",
                    "last": "ໜ້າສຸດທ້າຍ",
                    "next": "ໜ້າຕໍ່ໄປ",
                    "previous": "ໜ້າກ່ອນ"
                },
                "processing": "ກຳລັງປະມວນຜົນ...",
                "loadingRecords": "ກຳລັງໂຫຼດ...",
                "emptyTable": "ບໍ່ມີຂໍ້ມູນໃນຕາຕະລາງ"
            },
            "pageLength": 10,
            "order": [
                [4, "desc"]
            ], // ເລຽງລຳດັບຕາມວັນທີຊຳລະ (ລ່າສຸດກ່ອນ)
            "responsive": true
        });
    });

    function deletePayment(payment_id, payment_number) {
        swal({
                title: "ເຈົ້າຕ້ອງການລືບແທ້ ຫຼື ບໍ່?",
                text: "ເມື່ອລືບແລ້ວຈະບໍ່ສາມາດກູ້ຄືນໄດ້!",
                icon: "warning",
                buttons: true,
                dangerMode: true,
                buttons: ['ຍົກເລີກ', 'ຕົກລົງ']
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "payment-delete.php",
                        method: "post",
                        data: {
                            payment_id: payment_id,
                            payment_number: payment_number
                        },
                        success: function(data) {
                            if (data) {
                                alert(data);
                            } else {
                                swal("ສໍາເລັດ", "ຂໍ້ມູນຖືກລືບອອກຈາກຖານຂໍ້ມູນແລ້ວ", "success", {
                                    button: "ຕົກລົງ",
                                });
                                setTimeout(function() {
                                    location.reload();
                                }, 1000); //1000 = 1ວິນາທີ
                            }
                        }
                    });

                } else {
                    swal("ຂໍ້ມູນຂອງທ່ານຍັງປອດໄພ!", {
                        button: "ຕົກລົງ",
                    });
                }
            });
    }
</script>

</html>