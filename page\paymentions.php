<?php
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    include_once 'login-check.php';
    require_once '../function/function.php';
    require_once 'connect-db.php';
    //ດຶງລາຄາຫ້ອງທີ່ເລືອກຈ່າຍເງິນ
    $sql_room    = "SELECT Price FROM room WHERE Room_ID = '$room_id'";
    $result_room = mysqli_query($link, $sql_room);
    $room_data   = mysqli_fetch_assoc($result_room);
    $room_price  = $room_data['Price'];
    //ດຶງຂໍ້ມູນຈ່າຍເງິນ
    $sql = "SELECT p.*, s.Stu_name, r.R_number
            FROM payments p
            JOIN student s ON p.Stu_id = s.Stu_ID
            JOIN room r ON p.room_id = r.Room_ID
            ORDER BY p.payment_date DESC";
    $result = mysqli_query($link, $sql);
    $payment_data = [];
    if ($result) {
        $payment_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
    }
    // ສ້າງເລກທີ່ຈ່າຍເງິນ
    function generatepaymentNumber()
    {
        global $link;
        $datePart = date('Ymd');
        $prefix   = "RCP-" . $datePart;

        $sql    = "SELECT MAX(payment_number) AS payment_number FROM payments WHERE payment_number LIKE '$prefix%'";
        $result = mysqli_query($link, $sql);
        $row    = mysqli_fetch_assoc($result);

        if (empty($row['payment_number'])) {
            $payment_number = $prefix . "-001";
        } else {
            $lastId         = substr($row['payment_number'], -3); // ດຶງ 3 ຕົວສຸດທ້າຍ
            $newId          = str_pad((int) $lastId + 1, 3, "0", STR_PAD_LEFT);
            $payment_number = $prefix . "-" . $newId;
        }
        return $payment_number;
    }

    //ບັນທຶກການຈ່າຍເງິນ
    if (isset($_POST['btnPay'])) {
        $Stu_id         = data_input($_POST['Stu_id']);
        $room_id        = data_input($_POST['room_id']);
        $qty            = data_input($_POST['qty']);
        $payment_method = data_input($_POST['payment_method']);
        $note           = data_input($_POST['note']);

        // ລວມຈຳນວນເງິນ
        $total_amount = $qty * $room_price;

        // ສ້າງເລກທີ່ຈ່າຍເງິນ
        $payment_number = generatepaymentNumber();
        // ລົງລະຫັດໃບເສັດຮັບເງິນ
        $sql = "INSERT INTO payments (Stu_id, room_id, payment_number, payment_date, amount, payment_method, note)
                VALUES ('$Stu_id', '$room_id', '$payment_number', NOW(), '$total_amount', '$payment_method', '$note')";
        $result = mysqli_query($link, $sql);
        if ($result) {
            $message = '<script>swal("ສຳເລັດ", "ຊຳລະເງິນສຳເລັດ", "success",{button: "ຕົກລົງ"});</script>';
        } else {
            echo mysqli_error($link);
        }
    }

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paymentions || ຈ່າຍເງິນ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.complete.min.css">
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap5.css"> 

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">

    <style>
    .ts-dropdown .ts-dropdown-content {
        max-height: 80px;
        overflow-y: auto;
    }
    </style>

</head>

<body class="sb-nav-fixed">
    <?php include_once 'menu.php'?>
    <?php echo @$message ?>
    <div id="layoutSidenav_content">
        <div class="container-fluid mt-2">
            <div class="row">
                <div class="col-md-4 mt-4">
                    <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">ຈ່າຍເງິນ</legend>
                        <form method="post" enctype="multipart/form-data">
                            <div class="col-md-12">
                                <label for="Stu_id">ນັກສຶກສາ</label>
                                <select name="Stu_id" id="Stu_id" class="form-select" required>
                                    <option value="">--ເລືອກ--</option>
                                    <?php
                                        $sql    = "SELECT Stu_ID, Stu_name FROM student WHERE status = 'ບັນທຶກໃໝ່'";
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            echo "<option value='{$row['Stu_ID']}'>{$row['Stu_name']}</option>";
                                        }
                                    ?>
                                </select>
                                <div class="text-danger"><?php echo @$error_Stu_id ?></div>
                            </div>
                           
                            <div class="col-md-12">
                                <label for="qty">ຈຳນວນເດືອນ</label>
                                <input type="number" name="qty" id="qty" class="form-control" min="4" max="12" step="4" required>
                            </div>

                            <div class="col-12">
                                <label for="payment_method">ວິທີຊຳລະ</label>
                                <select name="payment_method" id="payment_method" class="form-select" required>
                                    <option value="1">ເງິນສົດ</option>
                                    <option value="0">ໂອນເງິນ</option>
                                </select>

                            </div>
                            <div class="col-md-12">
                                <label for="note">ໝາຍເຫດ</label>
                                <textarea name="note" id="note" class="form-control"></textarea>
                            </div><br>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" name="btnPay">ຈ່າຍເງິນ</button>
                            </div>

                        </form>
                    </fieldset>
                </div>
                <div class="col">
                    <h3>ປະຫວັດການຈ່າຍເງິນ</h3>
                    <table class="table table-striped-columns table-hover table-bordered border-black" id="example">
                        <thead>
                            <tr class="table-primary">
                                <th>ລຳດັບ</th>
                                <th>ຊື່</th>
                                <th>ຫ້ອງ</th>
                                <th>ຈຳນວນເງິນ</th>
                                <th>ວັນທີ່ຊຳລະ</th>
                                <th>ເລກທີ່ການຊຳລະ</th>
                                <th>ວິທີຊຳລະ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($payment_data)): ?>
                                <?php foreach($payment_data as $i => $rows): ?>
                                <tr>
                                    <td><?= $i+1 ?></td>
                                    <td><?= $rows['Stu_name'] ?></td>
                                    <td><?= $rows['R_number'] ?></td>
                                    <td><?= number_format($rows['amount'], 2) ?> ກີບ</td>
                                    <td><?= date('d/m/Y H:i', strtotime($rows['payment_date'])) ?></td>
                                    <td><?= $rows['payment_number'] ?></td>
                                    <td><?= $rows['payment_method'] == 1 ? 'ເງິນສົດ' : 'ໂອນເງິນ' ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">ບໍ່ມີຂໍ້ມູນການຊຳລະເງິນ</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
</body>
<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script>
new TomSelect("#select-room", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫາຫ້ອງ",
    maxItems: 1
});

new TomSelect("#Stu_id", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກ",
    maxItems: 1
});
</script>
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap5.js"></script>
<script>
$(document).ready(function() {
    $('#example').DataTable({
        "language": {
            "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
            "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
            "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
            "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
            "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
            "search": "ຄົ້ນຫາ:",
            "paginate": {
                "first": "ໜ້າທຳອິດ",
                "last": "ໜ້າສຸດທ້າຍ",
                "next": "ໜ້າຕໍ່ໄປ",
                "previous": "ໜ້າກ່ອນ"
            },
            "processing": "ກຳລັງປະມວນຜົນ...",
            "loadingRecords": "ກຳລັງໂຫຼດ...",
            "emptyTable": "ບໍ່ມີຂໍ້ມູນໃນຕາຕະລາງ"
        },
        "pageLength": 10,
        "order": [[4, "desc"]], // ເລຽງລຳດັບຕາມວັນທີຊຳລະ (ລ່າສຸດກ່ອນ)
        "responsive": true
    });
});
</script>
</html>