<?php
require_once '../function/function.php';
require_once 'connect-db.php';
include_once 'login-check.php';

//ດຶງຂໍ້ມູນການຈ່າຍເງິນ
$sql = "SELECT p.*, s.name, r.number
        FROM payments p
        JOIN student s ON p.student_id = s.id
        JOIN room r ON p.room_id = r.id
        ORDER BY p.payment_date DESC";
$payment_result = mysqli_query($link, $sql);
// Function: คำนวณจำนวนเงิน
function calculateAmount($qty, $room_price)
{
    $map = [4 => 1, 8 => 2, 12 => 3];
    if (! isset($map[$qty])) {
        throw new Exception("ຈຳນວນງວດບໍ່ຖືກຕ້ອງ");
    }
    return $map[$qty] * $room_price;
}
  // ສ້າງເລກທີ່ຈ່າຍເງິນ
 function generatePaymentNumber()
{
    global $link;
    $datePart = date('Ymd');
    $prefix   = "RCE-" . $datePart;

    $sql    = "SELECT MAX(payment_number) AS payment_number FROM payments WHERE payment_number LIKE '$prefix%'";
    $result = mysqli_query($link, $sql);
    $row    = mysqli_fetch_assoc($result);

    if (empty($row['payment_number'])) {
        $payment_number = $prefix . "-001";
    } else {
        $lastId         = substr($row['payment_number'], -3); // ດຶງ 3 ຕົວສຸດທ້າຍ
        $newId          = str_pad((int) $lastId + 1, 3, "0", STR_PAD_LEFT);
        $payment_number = $prefix . "-" . $newId;
    }
    return $payment_number;
}
//ດຶງການລະຫັດຫ້ອງຈາກນັກສຶກສາ
function getRoomId($Stu_id)
{
    global $link;
    $sql  = "SELECT room_id FROM student WHERE id = ?";
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "i", $Stu_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row    = mysqli_fetch_assoc($result);
    return $row['room_id'];
}
//ດຶງຂໍ້ມູນລາຄາຈາກຫ້ອງ
function getRoomPrice($room_id)
{
    global $link;
    $sql  = "SELECT price FROM room WHERE id = ?";
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "i", $room_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row    = mysqli_fetch_assoc($result);
    return $row['price'];
}

if (isset($_POST['btnPay'])) {
    $Stu_id         = isset($_POST['id']) ? data_input($_POST['id']) : '';
    $qty            = isset($_POST['qty']) ? data_input($_POST['qty']) : '';
    $payment_method = isset($_POST['payment_method']) ? data_input($_POST['payment_method']) : '';
    $note           = isset($_POST['note']) ? data_input($_POST['note']) : '';

    // ตรวจสอบข้อมูล
    if (empty($Stu_id)) {
        $error_Stu_id = "ກະລຸນາເລືອກນັກສຶກສາ";
    }
    if (empty($qty)) {
        $error_qty = "ກະລຸນາເລືອກຈຳນວນງວດ";
    }
    if (empty($payment_method)) {
        $error_payment_method = "ກະລຸນາເລືອກວິທີຊຳລະ";
    }

    if (empty($error_Stu_id) && empty($error_qty) && empty($error_payment_method)) {
        try {
            mysqli_begin_transaction($link);

            // 1. ລົງລາຄາຫ້ອງ
            $room_id           = getRoomId($Stu_id);
            $room_price        = getRoomPrice($room_id);
            $amount            = calculateAmount($qty, $room_price);
            $payment_number    = generatePaymentNumber();
            $payment_date      = date('Y-m-d H:i:s');
            $next_payment_date = date('Y-m-d', strtotime('+4 month'));

            // 2. ລົງການຊຳລະເງິນ
            $sql = "INSERT INTO payments (student_id, room_id,payment_number,qty,payment_date,next_payment_date, amount, payment_method, note)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($link, $sql);
            mysqli_stmt_bind_param($stmt, "iiddssdss", $Stu_id, $room_id, $payment_number, $qty, $payment_date, $next_payment_date, $amount, $payment_method, $note);
            mysqli_stmt_execute($stmt);
            if (! $stmt) {
                throw new Exception(mysqli_error($link));
            }

            // Commit transaction
            mysqli_commit($link);

            $message = '<script>swal("ສຳເລັດ", "ຊຳລະເງິນສຳເລັດ", "success",{button: "ຕົກລົງ"}).then(function() {
                window.location = "paymentions.php";
            });</script>';
        } catch (Exception $e) {
            mysqli_rollback($link);
            echo "ຂໍ້ຜິດພາດ: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paymentions || ຈ່າຍເງິນ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.complete.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap5.css">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">

    <style>
        .ts-dropdown .ts-dropdown-content {
            max-height: 80px;
            overflow-y: auto;
        }
    </style>

</head>

<body>
    <div class="sb-nav-fixed">

        <?php echo @$message ?>
        <?php if (isset($error_message) && ! empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        <div id="layoutSidenav_content">
            <div class="container-fluid mt-2">
                <div class="row">
                    <div class="col-md-4 mt-4">
                        <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                            <legend class="float-none w-auto p-2 h5">ຈ່າຍເງິນ</legend>
                            <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <div class="col-md-12">
                                    <label for="id">ນັກສຶກສາ</label>
                                    <select name="id" id="id" class="form-select" required>
                                        <option value="">--ເລືອກ--</option>
                                        <?php
                                        $sql    = "SELECT id, name FROM student WHERE is_active = '1'";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                echo "<option value='{$row['id']}'>{$row['name']}</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                    <div class="text-danger"><?php echo @$error_Stu_id ?></div>
                                </div>

                                <div class="col-md-12">
                                    <label for="qty">ຈຳນວນງວດ</label>
                                    <input type="number" name="qty" id="qty" class="form-control" min="4" max="12" step="4" required>
                                    <div class="text-danger"><?php echo @$error_qty ?></div>
                                </div>

                                <div class="col-12">
                                    <label for="payment_method">ວິທີຊຳລະ</label>
                                    <select name="payment_method" id="payment_method" class="form-select" required>
                                        <option value="1">ເງິນສົດ</option>
                                        <option value="0">ໂອນເງິນ</option>
                                    </select>
                                    <div class="text-danger"><?php echo @$error_payment_method ?></div>
                                </div>
                                <div class="col-md-12">
                                    <label for="note">ໝາຍເຫດ</label>
                                    <textarea name="note" id="note" class="form-control"></textarea>
                                </div><br>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" name="btnPay">ຈ່າຍເງິນ</button>
                                </div>

                            </form>
                        </fieldset>
                    </div>
                    <div class="col">
                        <h3>ປະຫວັດການຈ່າຍເງິນ</h3>
                        <table class="table table-striped-columns table-hover table-bordered border-black" id="example">
                            <thead>
                                <tr class="table-primary">
                                    <th width="5%">ລຳດັບ</th>
                                    <th width="10%">ຊື່</th>
                                    <th width="5%">ຫ້ອງ</th>
                                    <th width="15%">ຈຳນວນເງິນ</th>
                                    <th width="15%">ວັນທີ່ຊຳລະ</th>
                                    <th width="15%">ໝົດງວດ</th>
                                    <th width="15%">ເລກທີ່ການຊຳລະ</th>
                                    <th width="5%">ວິທີຊຳລະ</th>
                                    <th width="15%">ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = mysqli_fetch_assoc($payment_result)) { ?>
                                    <?php $payment_data[] = $row; ?>
                                    <tr>
                                        <td><?php echo $i++; ?></td>
                                        <td><?php echo ! empty($row['name']) ? $row['name'] : '-' ?></td>
                                        <td><?php echo ! empty($row['number']) ? $row['number'] : '-' ?></td>
                                        <td><?php echo isset($row['amount']) ? number_format($row['amount'], 2) . ' ກີບ' : '-' ?></td>
                                        <td><?php echo ! empty($row['payment_date']) ? date('d/m/Y H:i', strtotime($row['payment_date'])) : '-' ?></td>
                                        <td><?php echo ! empty($row['next_payment_date']) ? date('d/m/Y', strtotime($row['next_payment_date'])) : '-' ?></td>
                                        <td><?php echo ! empty($row['payment_number']) ? $row['payment_number'] : '-' ?></td>
                                        <td><?php echo isset($row['payment_method']) ? ($row['payment_method'] == 1 ? 'ເງິນສົດ' : 'ໂອນເງິນ') : '-' ?></td>
                                        <td>
                                            <a href="paymentions-print.php?id=<?php echo $row['id'] ?? '' ?>" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-print"></i> ພິມບິນ
                                            </a>
                                            <?php if ($_SESSION['role'] == 'admin'): ?>
                                                <button onclick="deletePayment('<?php echo $row['id'] ?? '' ?>','<?php echo $row['payment_number'] ?? '' ?>')" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash-alt"></i> ລຶບ
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php } ?>

                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
</body>
<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap5.js"></script>
<script>
    new TomSelect("#id", {
        create: false,
        sortField: {
            field: "text",
            direction: "asc"
        },
        placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກ",
        maxItems: 1
    });
    $(document).ready(function() {
        $('#example').DataTable({
            "language": {
                "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
                "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
                "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
                "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
                "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
                "search": "ຄົ້ນຫາ:",
                "paginate": {
                    "first": "ໜ້າທຳອິດ",
                    "last": "ໜ້າສຸດທ້າຍ",
                    "next": "ໜ້າຕໍ່ໄປ",
                    "previous": "ໜ້າກ່ອນ"
                },
                "processing": "ກຳລັງປະມວນຜົນ...",
                "loadingRecords": "ກຳລັງໂຫຼດ...",
                "emptyTable": "ບໍ່ມີຂໍ້ມູນໃນຕາຕະລາງ"
            },
            "pageLength": 10,
            "order": [
                [4, "desc"]
            ], // ເລຽງລຳດັບຕາມວັນທີຊຳລະ (ລ່າສຸດກ່ອນ)
            "responsive": true
        });
    });

    function deletePayment(payment_id, payment_number) {
        swal({
                title: "ເຈົ້າຕ້ອງການລືບແທ້ ຫຼື ບໍ່?",
                text: "ເມື່ອລືບແລ້ວຈະບໍ່ສາມາດກູ້ຄືນໄດ້!",
                icon: "warning",
                buttons: true,
                dangerMode: true,
                buttons: ['ຍົກເລີກ', 'ຕົກລົງ']
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "payment-delete.php",
                        method: "post",
                        data: {
                            payment_id: payment_id,
                            payment_number: payment_number
                        },
                        success: function(data) {
                            if (data) {
                                alert(data);
                            } else {
                                swal("ສໍາເລັດ", "ຂໍ້ມູນຖືກລືບອອກຈາກຖານຂໍ້ມູນແລ້ວ", "success", {
                                    button: "ຕົກລົງ",
                                });
                                setTimeout(function() {
                                    location.reload();
                                }, 1000); //1000 = 1ວິນາທີ
                            }
                        }
                    });

                } else {
                    swal("ຂໍ້ມູນຂອງທ່ານຍັງປອດໄພ!", {
                        button: "ຕົກລົງ",
                    });
                }
            });
    }
</script>

</html>