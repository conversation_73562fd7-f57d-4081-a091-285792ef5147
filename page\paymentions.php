<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// Function: คำนวณจำนวนเงิน
function calculateAmount($qty, $room_price) {
    $map = [4 => 1, 8 => 2, 12 => 3];
    if (!isset($map[$qty])) {
        throw new Exception("ຈຳນວນງວດບໍ່ຖືກຕ້ອງ");
    }
    return $map[$qty] * $room_price;
}

// Function: ดึงวันเริ่มนับจากงวดล่าสุด
function getStartDate($link, $Stu_id) {
    $sql = "SELECT next_payment_date FROM payments WHERE Stu_id = ? ORDER BY next_payment_date DESC LIMIT 1";
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "s", $Stu_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);
    return $row ? $row['next_payment_date'] : date('Y-m-d');
}

// Load payment history
$sql = "SELECT p.id, p.Stu_id, p.room_id, p.payment_number, p.payment_date,
           p.next_payment_date, p.amount, p.qty, p.payment_method, p.note,
           s.Stu_name, r.R_number
        FROM payments p
        JOIN student s ON p.Stu_id = s.Stu_ID
        JOIN room r ON p.room_id = r.Room_ID
        ORDER BY p.payment_date DESC";
$result = mysqli_query($link, $sql);
$payment_data = $result ? mysqli_fetch_all($result, MYSQLI_ASSOC) : [];

if (isset($_POST['btnPay'])) {
    try {
        $Stu_id         = data_input($_POST['Stu_id']);
        $qty            = (int)data_input($_POST['qty']);
        $payment_method = data_input($_POST['payment_method']);
        $note           = data_input($_POST['note']);

        // ตรวจสอบว่ามีการค้างจ่ายหรือไม่
        $check_sql = "SELECT COUNT(*) as cnt FROM payments WHERE Stu_id = ? AND is_active = 0";
        $check_stmt = mysqli_prepare($link, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "s", $Stu_id);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);
        $check = mysqli_fetch_assoc($check_result);
        if ($check['cnt'] > 0) {
            throw new Exception("ນັກສຶກສາຍັງມີການຄ້າງຊຳລະ!");
        }

        // ดึง room_id
        $sql_room_id = "SELECT room_id FROM payments WHERE Stu_id = ? ORDER BY id DESC LIMIT 1";
        $stmt_room_id = mysqli_prepare($link, $sql_room_id);
        mysqli_stmt_bind_param($stmt_room_id, "s", $Stu_id);
        mysqli_stmt_execute($stmt_room_id);
        $result_room_id = mysqli_stmt_get_result($stmt_room_id);
        $row_room_id = mysqli_fetch_assoc($result_room_id);
        $room_id = $row_room_id['room_id'];

        // ดึงราคาห้อง
        $sql_room = "SELECT Price FROM room WHERE Room_ID = ?";
        $stmt_room = mysqli_prepare($link, $sql_room);
        mysqli_stmt_bind_param($stmt_room, "s", $room_id);
        mysqli_stmt_execute($stmt_room);
        $result_room = mysqli_stmt_get_result($stmt_room);
        $room_data = mysqli_fetch_assoc($result_room);

        if (!$room_data) {
            throw new Exception("ບໍ່ພົບຂໍ້ມູນຫ້ອງ");
        }

        $room_price = $room_data['Price'];
        $total_amount = calculateAmount($qty, $room_price);
        $num = $qty / 4;

        mysqli_begin_transaction($link);

        // ปิดรายการเก่าด้วย is_active = 1
        $sql_update = "UPDATE payments SET is_active = 1 WHERE Stu_id = ? AND is_active = 0";
        $stmt_update = mysqli_prepare($link, $sql_update);
        mysqli_stmt_bind_param($stmt_update, "s", $Stu_id);
        mysqli_stmt_execute($stmt_update);

        // สร้าง payment_number
        $payment_number = generatepaymentNumber();

        // คำนวณวันที่เริ่มงวด
        $start_date = getStartDate($link, $Stu_id);

        // เพิ่มรายการใหม่
        $sql_insert = "INSERT INTO payments (Stu_id, room_id, payment_number, payment_date, next_payment_date, amount, qty, payment_method, note, is_active)
                       VALUES (?, ?, ?, NOW(), DATE_ADD(?, INTERVAL ? MONTH), ?, ?, ?, ?, 0)";
        $stmt_insert = mysqli_prepare($link, $sql_insert);
        mysqli_stmt_bind_param($stmt_insert, "sssisiiss", $Stu_id, $room_id, $payment_number, $start_date, $qty, $total_amount, $num, $payment_method, $note);
        $result_insert = mysqli_stmt_execute($stmt_insert);

        if (!$result_insert) {
            throw new Exception("ບັນທຶກຂໍ້ມູນຜິດພາດ: " . mysqli_error($link));
        }

        $payment_id = mysqli_insert_id($link);
        mysqli_commit($link);

        $message = '<script>swal("ສຳເລັດ", "ຊຳລະເງິນສຳເລັດ", "success", {button: "ຕົກລົງ"}).then(function() {
                        window.location = "paymentions-print.php?id=' . $payment_id . '";
                    });</script>';
        

    } catch (Exception $e) {
        mysqli_rollback($link);
        $error_message = $e->getMessage();
    }

    // reset form
    $Stu_id = $payment_number = $qty = $payment_method = $note = null;
    $room_price = 0;
    $total_amount = 0;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paymentions || ຈ່າຍເງິນ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.complete.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap5.css">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
    <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">

    <style>
        .ts-dropdown .ts-dropdown-content {
            max-height: 80px;
            overflow-y: auto;
        }
    </style>

</head>

<body>
    <div class="sb-nav-fixed">
      
<?php echo @$message ?>
<?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        <div id="layoutSidenav_content">
            <div class="container-fluid mt-2">
                <div class="row">
                    <div class="col-md-4 mt-4">
                        <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                            <legend class="float-none w-auto p-2 h5">ຈ່າຍເງິນ</legend>
                            <form method="post" enctype="multipart/form-data">
                                <div class="col-md-12">
                                    <label for="Stu_id">ນັກສຶກສາ</label>
                                    <select name="Stu_id" id="Stu_id" class="form-select" required>
                                        <option value="">--ເລືອກ--</option>
                                        <?php
                                            $sql    = "SELECT Stu_ID, Stu_name FROM student WHERE status = '1'";
                                            $result = mysqli_query($link, $sql);
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                echo "<option value='{$row['Stu_ID']}'>{$row['Stu_name']}</option>";
                                            }
                                        ?>
                                    </select>
                                    <div class="text-danger"><?php echo @$error_Stu_id ?></div>
                                </div>

                                <div class="col-md-12">
                                    <label for="qty">ຈຳນວນງວດ</label>
                                    <input type="number" name="qty" id="qty" class="form-control" min="4" max="12" step="4" required>
                                </div>

                                <div class="col-12">
                                    <label for="payment_method">ວິທີຊຳລະ</label>
                                    <select name="payment_method" id="payment_method" class="form-select" required>
                                        <option value="1">ເງິນສົດ</option>
                                        <option value="0">ໂອນເງິນ</option>
                                    </select>

                                </div>
                                <div class="col-md-12">
                                    <label for="note">ໝາຍເຫດ</label>
                                    <textarea name="note" id="note" class="form-control"></textarea>
                                </div><br>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" name="btnPay">ຈ່າຍເງິນ</button>
                                </div>

                            </form>
                        </fieldset>
                    </div>
                    <div class="col">
                        <h3>ປະຫວັດການຈ່າຍເງິນ</h3>
                        <table class="table table-striped-columns table-hover table-bordered border-black" id="example">
                            <thead>
                                <tr class="table-primary">
                                    <th width="5%">ລຳດັບ</th>
                                    <th width="10%">ຊື່</th>
                                    <th width="5%">ຫ້ອງ</th>
                                    <th width="15%">ຈຳນວນເງິນ</th>
                                    <th width="15%">ວັນທີ່ຊຳລະ</th>
                                    <th width="15%">ໝົດງວດ</th>
                                    <th width="15%">ເລກທີ່ການຊຳລະ</th>
                                    <th width="5%">ວິທີຊຳລະ</th>
                                    <th width="15%">ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (! empty($payment_data)): ?>
<?php foreach ($payment_data as $i => $rows): ?>
                                        <tr>
                                            <td class="text-start"><?php echo $i + 1 ?></td>
                                            <td class="text-start"><?php echo isset($rows['Stu_name']) ? $rows['Stu_name'] : '-' ?></td>
                                            <td class="text-center"><?php echo isset($rows['R_number']) ? $rows['R_number'] : '-' ?></td>
                                            <td class="text-start"><?php echo isset($rows['amount']) ? number_format($rows['amount'], 2) . ' ກີບ' : '-' ?></td>
                                            <td class="text-start"><?php echo isset($rows['payment_date']) ? date('d/m/Y H:i', strtotime($rows['payment_date'])) : '-' ?></td>
                                            <td class="text-start"><?php echo isset($rows['next_payment_date']) ? date('d/m/Y', strtotime($rows['next_payment_date'])) : '-' ?></td>
                                            <td class="text-start"><?php echo isset($rows['payment_number']) ? $rows['payment_number'] : '-' ?></td>
                                            <td class="text-center"><?php echo isset($rows['payment_method']) ? ($rows['payment_method'] == 1 ? 'ເງິນສົດ' : 'ໂອນເງິນ') : '-' ?></td>
                                            <td>
                                                <a href="paymentions-print.php?id=<?php echo isset($rows['id']) ? $rows['id'] : '' ?>" class="btn btn-info btn-sm" target="_blank">
                                                    <i class="fas fa-print"></i> ພິມບິນ
                                                </a>
                                                <?php if ($_SESSION['role'] == 'admin'): ?>
                                                    <button type="button" onclick="deletePayment('<?php echo isset($rows['id']) ? $rows['id'] : '' ?>', '<?php echo isset($rows['payment_number']) ? $rows['payment_number'] : '' ?>')" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash-alt"></i> ລຶບ
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
<?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">ບໍ່ມີຂໍ້ມູນການຊຳລະເງິນ</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
</body>
<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap5.js"></script>
<script>
        new TomSelect("#Stu_id", {
        create: false,
        sortField: {
            field: "text",
            direction: "asc"
        },
        placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກ",
        maxItems: 1
    });
    $(document).ready(function() {
        $('#example').DataTable({
            "language": {
                "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
                "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
                "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
                "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
                "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
                "search": "ຄົ້ນຫາ:",
                "paginate": {
                    "first": "ໜ້າທຳອິດ",
                    "last": "ໜ້າສຸດທ້າຍ",
                    "next": "ໜ້າຕໍ່ໄປ",
                    "previous": "ໜ້າກ່ອນ"
                },
                "processing": "ກຳລັງປະມວນຜົນ...",
                "loadingRecords": "ກຳລັງໂຫຼດ...",
                "emptyTable": "ບໍ່ມີຂໍ້ມູນໃນຕາຕະລາງ"
            },
            "pageLength": 10,
            "order": [
                [4, "desc"]
            ], // ເລຽງລຳດັບຕາມວັນທີຊຳລະ (ລ່າສຸດກ່ອນ)
            "responsive": true
        });
    });

    function deletePayment(payment_id, payment_number) {
        swal({
                title: "ເຈົ້າຕ້ອງການລືບແທ້ ຫຼື ບໍ່?",
                text: "ເມື່ອລືບແລ້ວຈະບໍ່ສາມາດກູ້ຄືນໄດ້!",
                icon: "warning",
                buttons: true,
                dangerMode: true,
                buttons: ['ຍົກເລີກ', 'ຕົກລົງ']
            })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: "payment-delete.php",
                        method: "post",
                        data: {
                            payment_id: payment_id,
                            payment_number: payment_number
                        },
                        success: function(data) {
                            if (data) {
                                alert(data);
                            } else {
                                swal("ສໍາເລັດ", "ຂໍ້ມູນຖືກລືບອອກຈາກຖານຂໍ້ມູນແລ້ວ", "success", {
                                    button: "ຕົກລົງ",
                                });
                                setTimeout(function() {
                                    location.reload();
                                }, 1000); //1000 = 1ວິນາທີ
                            }
                        }
                    });

                } else {
                    swal("ຂໍ້ມູນຂອງທ່ານຍັງປອດໄພ!", {
                        button: "ຕົກລົງ",
                    });
                }
            });
    }
</script>

</html>