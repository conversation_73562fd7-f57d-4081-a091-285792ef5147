<?php
session_start();
require_once 'connect-db.php';

// ตรวจสอบว่า ้ใช้ล็อกแล้ว ไม่
if (!isset($_SESSION['user_id'])) {
    header("Location: login-form.php");
    exit;
}

$user_id = $_SESSION['user_id'];
$message = "";
$message_type = "";

// ข้อมูล ้ใช้ แสดงผล
$sql = "SELECT name, username, role FROM users WHERE user_id = '$user_id'";
$result = mysqli_query($link, $sql);
$user = mysqli_fetch_assoc($result);

// การเปลี่ยน ผ่านลงในระบบล็อก
function logPasswordChange($link, $user_id) {
    $action = "เปลี่ยน ผ่าน";
    $action_date = date('Y-m-d H:i:s');
    
    $sql = "INSERT INTO system_logs (user_id, action, action_date) VALUES ('$user_id', '$action', '$action_date')";
    mysqli_query($link, $sql);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // ตรวจสอบความยาว ผ่าน
    if (strlen($new_password) < 6) {
        $message = "❌ ລະຫັດຜ່ານໃໝ່ຕ້ອງມີຢ່າງໜ້ອຍ 6 ຕົວອັກສອນ";
        $message_type = "danger";
    } 
    // ตรวจสอบการ ผ่าน
    elseif ($new_password !== $confirm_password) {
        $message = "❌ ລະຫັດໃໝ່ບໍ່ກົງກັນ";
        $message_type = "danger";
    } 
    // ตรวจสอบ ผ่าน:และ ปเดตหาก ต้อง
    else {
        // ผ่านจากฐานข้อมูล
        $sql = "SELECT password FROM users WHERE user_id = '$user_id'";
        $result = mysqli_query($link, $sql);
        $user_data = mysqli_fetch_assoc($result);
        
        $current_password_md5 = md5($current_password);
        
        if ($user_data && $current_password_md5 == $user_data['password']) {
            // ตรวจสอบว่า ผ่านใหม่แตกต่างจากรหัสผ่าน
            $new_password_md5 = md5($new_password);
            
            if ($new_password_md5 == $user_data['password']) {
                $message = "❌ ລະຫັດໃໝ່ຕ້ອງແຕກຕ່າງຈາກລະຫັດປັດຈຸບັນ";
                $message_type = "danger";
            } else {
                // ปเดตรหัสผ่านในฐานข้อมูล
                $sql = "UPDATE users SET password = '$new_password_md5' WHERE user_id = '$user_id'";
                $result = mysqli_query($link, $sql);
                
                if ($result) {
                    // ปเดตรหัสผ่านในเซส
                    $_SESSION['password'] = $new_password_md5;
                    
                    // การเปลี่ยน ผ่านลงในระบบล็อก
                    logPasswordChange($link, $user_id);
                    
                    $message = "✅ ປ່ຽນລະຫັດຜ່ານສຳເລັດ";
                    $message_type = "success";
                    
                    // ล้างข้อมูลฟอร์ม ปเดตสำเร็จ
                    $_POST = array();
                } else {
                    $message = "❌ ເກີດຂໍ້ຜິດພາດໃນການປ່ຽນລະຫັດຜ່ານ";
                    $message_type = "danger";
                }
            }
        } else {
            $message = "❌ ລະຫັດປັດຈຸບັນບໍ່ຖືກຕ້ອງ";
            $message_type = "danger";
        }
    }
}

// กำหนดเลย์เอาต์ตามบทบาทของ ้ใช้
$is_admin_layout = ($user['role'] == 'admin');
?>

<!DOCTYPE html>
<html lang="la">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ປ່ຽນລະຫັດຜ່ານ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <?php if ($is_admin_layout): ?>
    <link href="../css/styles.css" rel="stylesheet" />
    <?php endif; ?>
    <style>
        .password-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .password-card .card-header {
            background-color: #4e73df;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .password-strength {
            height: 5px;
            margin-top: 5px;
            transition: all 0.3s;
        }
        .btn-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body class="<?php echo $is_admin_layout ? 'sb-nav-fixed' : ''; ?>">
    <?php if ($is_admin_layout): ?>
        <?php include_once 'menu.php'?>
        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid px-4 py-4">
    <?php else: ?>
        <div class="container py-5">
    <?php endif; ?>
    
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card password-card">
                <div class="card-header">
                    <h4 class="m-0"><i class="fas fa-key me-2"></i>ປ່ຽນລະຫັດຜ່ານ</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($message)): ?>
                        <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
                            <?= $message ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" id="passwordForm">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">ລະຫັດປັດຈຸບັນ</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">ລະຫັດໃໝ່</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength w-100 bg-secondary rounded"></div>
                            <small class="form-text text-muted">ລະຫັດຜ່ານຕ້ອງມີຢ່າງໜ້ອຍ 6 ຕົວອັກສອນ</small>
                        </div>
                        
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">ຢືນຢັນລະຫັດໃໝ່</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="confirm_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div id="passwordMatch" class="form-text"></div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="profiles.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left btn-icon"></i>ຍ້ອນກັບ
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save btn-icon"></i>ບັນທຶກ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card password-card mt-4">
                <div class="card-header">
                    <h5 class="m-0"><i class="fas fa-info-circle me-2"></i>ຄຳແນະນຳດ້ານຄວາມປອດໄພ</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            ໃຊ້ລະຫັດຜ່ານທີ່ມີຄວາມຍາວຢ່າງໜ້ອຍ 8 ຕົວອັກສອນ
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            ປະສົມຕົວອັກສອນພິມໃຫຍ່ ແລະ ພິມນ້ອຍ
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            ເພີ່ມຕົວເລກ ແລະ ສັນຍາລັກພິເສດ (!, @, #, $, %)
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            ຫຼີກລ້ຽງການໃຊ້ຂໍ້ມູນສ່ວນຕົວທີ່ຄາດເດົາໄດ້ງ່າຍ
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <?php if ($is_admin_layout): ?>
                </div>
            </main>
        </div>
    <?php else: ?>
        </div>
    <?php endif; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>
    <?php if ($is_admin_layout): ?>
    <script src="../js/scripts.js"></script>
    <?php endif; ?>
    <script>
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Password strength indicator
        const newPassword = document.getElementById('new_password');
        const strengthBar = document.querySelector('.password-strength');
        
        newPassword.addEventListener('input', function() {
            const value = this.value;
            let strength = 0;
            
            if (value.length >= 6) strength += 20;
            if (value.length >= 8) strength += 20;
            if (/[A-Z]/.test(value)) strength += 20;
            if (/[0-9]/.test(value)) strength += 20;
            if (/[^A-Za-z0-9]/.test(value)) strength += 20;
            
            strengthBar.style.width = strength + '%';
            
            if (strength <= 40) {
                strengthBar.classList.remove('bg-warning', 'bg-success');
                strengthBar.classList.add('bg-danger');
            } else if (strength <= 80) {
                strengthBar.classList.remove('bg-danger', 'bg-success');
                strengthBar.classList.add('bg-warning');
            } else {
                strengthBar.classList.remove('bg-danger', 'bg-warning');
                strengthBar.classList.add('bg-success');
            }
        });
        
        // Password match indicator
        const confirmPassword = document.getElementById('confirm_password');
        const passwordMatch = document.getElementById('passwordMatch');
        
        function checkPasswordMatch() {
            if (newPassword.value && confirmPassword.value) {
                if (newPassword.value === confirmPassword.value) {
                    passwordMatch.textContent = "✓ ລະຫັດຜ່ານກົງກັນ";
                    passwordMatch.classList.remove('text-danger');
                    passwordMatch.classList.add('text-success');
                } else {
                    passwordMatch.textContent = "✗ ລະຫັດຜ່ານບໍ່ກົງກັນ";
                    passwordMatch.classList.remove('text-success');
                    passwordMatch.classList.add('text-danger');
                }
            } else {
                passwordMatch.textContent = "";
            }
        }
        
        newPassword.addEventListener('input', checkPasswordMatch);
        confirmPassword.addEventListener('input', checkPasswordMatch);
        
        // Form validation
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            if (newPassword.value !== confirmPassword.value) {
                e.preventDefault();
                alert('ລະຫັດຜ່ານບໍ່ກົງກັນ. ກະລຸນາກວດສອບອີກຄັ້ງ.');
            }
        });
    </script>
</body>
</html>
