<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// กำหนดค่าเริ่มต้น<|im_start|>กรอง
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); //3แรกของ3
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); //3<|im_start|>ท้ายของ3
$building = isset($_GET['building']) ? $_GET['building'] : 'all';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';

// สร้าง SQL query 3้นฐาน
$sql = "SELECT rm.*, s.Stu_name, s.tell, s.gender, r.R_number, r.Build, r.Price
        FROM room_members rm
        JOIN student s ON rm.Stu_id = s.Stu_ID
        JOIN room r ON rm.room_id = r.Room_ID
        WHERE rm.check_in_date BETWEEN '$start_date' AND '$end_date'";

// เราะ่มเงื่อนไขการกรอง
if ($building != 'all') {
    $sql .= " AND r.Build = '$building'";
}

if ($status != 'all') {
    if ($status == 'active') {
        $sql .= " AND rm.is_active = 1";
    } else {
        $sql .= " AND rm.is_active = '0'";
    }
}

$sql .= " ORDER BY rm.check_in_date DESC, rm.id DESC";

$result = mysqli_query($link, $sql);

// คำนวณ3ยอด
$total_active = 0;
$total_inactive = 0;
$total_members = 0;

$temp_result = mysqli_query($link, $sql);
while ($row = mysqli_fetch_assoc($temp_result)) {
    $total_members++;
    
  if ($row['is_active'] == 1) {
    $total_active++;
} else {
        $total_inactive++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>ລາຍງານການເຂົ້າ-ອອກຫ້ອງພັກ</title>
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <link href="../../css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    <style>
        @media print {
            .no-print {
                display: none;
            }
            .table-responsive {
                overflow: visible !important;
            }
        }
    </style>
</head>

<body class="sb-nav-fixed">
    <?php include 'menu.php'; ?>
    <div id="layoutSidenav_content">
        <main>
            <div class="container-fluid px-4">
                <h1 class="mt-4">ລາຍງານການເຂົ້າ-ອອກຫ້ອງພັກ</h1>
                <ol class="breadcrumb mb-4">
                    <li class="breadcrumb-item"><a href="../index.php">ໜ້າຫຼັກ</a></li>
                    <li class="breadcrumb-item active">ລາຍງານການເຂົ້າ-ອອກຫ້ອງພັກ</li>
                </ol>

                <!-- ຟອມຄົ້ນຫາ -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <i class="fas fa-filter me-1"></i>
                        ຕົວກອງຂໍ້ມູນ
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">ວັນທີເລີ່ມຕົ້ນ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">ວັນທີສິ້ນສຸດ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="building" class="form-label">ຕຶກ</label>
                                <select class="form-select" id="building" name="building">
                                    <option value="all" <?php echo $building == 'all' ? 'selected' : ''; ?>>ທັງໝົດ</option>
                                    <?php
                                    $sql_building = "SELECT DISTINCT Build FROM room ORDER BY Build";
                                    $result_building = mysqli_query($link, $sql_building);
                                    while ($row_building = mysqli_fetch_assoc($result_building)) {
                                        $selected = ($building == $row_building['Build']) ? 'selected' : '';
                                        echo "<option value='{$row_building['Build']}' $selected>ຕຶກ {$row_building['Build']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">ສະຖານະ</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status == 'all' ? 'selected' : ''; ?>>ທັງໝົດ</option>
                                    <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>ກຳລັງພັກຢູ່</option>
                                    <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>ອອກແລ້ວ</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> ຄົ້ນຫາ
                                </button>
                                <button type="button" class="btn btn-success" onclick="window.print()">
                                    <i class="fas fa-print"></i> ພິມ
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- ສະຫຼຸບຂໍ້ມູນ -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6">
                        <div class="card bg-primary text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-0"><?php echo $total_members; ?></h4>
                                        <div>ຈຳນວນນັກສຶກສາທັງໝົດ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-users fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6">
                        <div class="card bg-success text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-0"><?php echo $total_active; ?></h4>
                                        <div>ກຳລັງພັກຢູ່</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-bed fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6">
                        <div class="card bg-danger text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-0"><?php echo $total_inactive; ?></h4>
                                        <div>ອອກແລ້ວ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-sign-out-alt fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ຕາຕະລາງຂໍ້ມູນ -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-table me-1"></i>
                        ລາຍງານການເຂົ້າ-ອອກຫ້ອງພັກ (<?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?>)
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="dataTable" width="100%" cellspacing="0">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th>ລຳດັບ</th>
                                        <th>ລະຫັດນັກສຶກສາ</th>
                                        <th>ຊື່-ນາມສະກຸນ</th>
                                        <th>ເພດ</th>
                                        <th>ເບີໂທ</th>
                                        <th>ຫ້ອງພັກ</th>
                                        <th>ວັນທີເຂົ້າພັກ</th>
                                        <th>ໄລຍະເວລາ</th>
                                        <th>ສະຖານະ</th>
                                        <th class="no-print">ຈັດການ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $i = 1;
                                    mysqli_data_seek($result, 0);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        // ຄຳນວນໄລຍະເວລາພັກ
                                        $check_in_date = new DateTime($row['check_in_date']);
                                        $today = new DateTime();
                                        $interval = $check_in_date->diff($today);
                                        $days = $interval->days;
                                    ?>
                                        <tr>
                                            <td><?php echo $i++; ?></td>
                                            <td><?php echo $row['Stu_id']; ?></td>
                                            <td><?php echo $row['Stu_name']; ?></td>
                                            <td><?php echo $row['gender'] == 'ຊ' ? 'ຊາຍ' : 'ຍິງ'; ?></td>
                                            <td><?php echo $row['tell']; ?></td>
                                            <td>ຕຶກ <?php echo $row['Build']; ?> ຫ້ອງ <?php echo $row['R_number']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($row['check_in_date'])); ?></td>
                                            <td><?php echo $row['qty']; ?> ເດືອນ (<?php echo $days; ?> ວັນ)</td>
                                            <td>
                                                <?php if ($row['is_active'] == 1) : ?>
                                                    <span class="badge bg-success">ກຳລັງພັກຢູ່</span>
                                                <?php else : ?>
                                                    <span class="badge bg-danger">ອອກແລ້ວ</span>
                                                    <div class="small"><?php echo date('d/m/Y', strtotime($row['check_out_date'])); ?></div>
                                                <?php endif; ?>
                                            </td>
                                          
                                            <td class="no-print">
                                                <a href="../room-members.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye"></i> ເບິ່ງ
                                                </a>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <footer class="py-4 bg-light mt-auto">
            <div class="container-fluid px-4">
                <div class="d-flex align-items-center justify-content-between small">
                    <div class="text-muted">ລະບົບຈັດການຫໍພັກ &copy; 2025</div>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js" crossorigin="anonymous"></script>
    <script>
        // Initialize DataTable
        window.addEventListener('DOMContentLoaded', event => {
            const datatablesSimple = document.getElementById('dataTable');
            if (datatablesSimple) {
                new simpleDatatables.DataTable(datatablesSimple, {
                    labels: {
                        placeholder: "ຄົ້ນຫາ...",
                        perPage: "ສະແດງລາຍການຕໍ່ໜ້າ",
                        noRows: "ບໍ່ພົບຂໍ້ມູນ",
                        info: "ສະແດງ {start} ຫາ {end} ຈາກ {rows} ລາຍການ",
                    }
                });
            }
        });
    </script>
</body>
</html>