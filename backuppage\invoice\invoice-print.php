<?php
include_once '../login-check.php';
require_once '../../function/function.php';
require_once '../connect-db.php';

if (!isset($_GET['id'])) {
    echo "<script>alert('ไม่พบข้อมูลใบแจ้งหนี้'); window.location = 'room-members.php';</script>";
    exit;
}

$invoice_id = (int) $_GET['id'];

$sql = "SELECT i.*, s.Stu_name, s.tell, r.R_number, r.Build, r.Price, m.note
        FROM invoices i
        JOIN room_members m ON i.Stu_id = m.Stu_id
        JOIN student s ON i.Stu_id = s.Stu_ID
        JOIN room r ON i.room_id = r.Room_ID
        WHERE i.id = '$invoice_id'";
$result = mysqli_query($link, $sql);

if (mysqli_num_rows($result) == 0) {
    echo "<script>alert('ไม่พบข้อมูลใบแจ้งหนี้'); window.location = 'room-members.php';</script>";
    exit;
}

$row = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ໃບແຈ້ງໜີ້ <?= $row['invoice_number'] ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            font-size: 14px;
        }

        @media print {
            @page {
                size: auto; /* ให้ผู้ใช้เลือก A4 หรือ A5 */
                /* margin: 10mm; */
            }

            .no-print {
                display: none;
            }

            body {
                font-size: 13px;
            }

            .container {
                max-width: 700px; /* ขนาดเหมาะกับ A5 แนวตั้ง */
                margin: 0 auto;
            }
        }

        .invoice-title {
            font-size: 24px;
            font-weight: bold;
        }

        .invoice-number, .invoice-date {
            font-size: 14px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        .invoice-table th {
            background-color: #f2f2f2;
            text-align: center;
        }

        .payment-info {
            margin-top: 20px;
            padding: 10px;
            border: 1px dashed #ccc;
            background-color: #f9f9f9;
        }

        .invoice-footer {
            margin-top: 30px;
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row no-print mb-3">
            <div class="col-12">
                <button onclick="window.print()" class="btn btn-primary">ພິມໃບແຈ້ງໜີ້</button>
                <a href="../room-members.php" class="btn btn-secondary">ກັບຄືນ</a>
                <a href="../payment.php?invoice_id=<?= $invoice_id ?>" class="btn btn-success">ໄປຊຳລະ</a>
                <span class="ml-2 text-muted">(* ເລືອກ A4 ຫຼື A5 ເວລາພິມ)</span>
            </div>
        </div>

        <div class="text-center mb-3">
            <img src="../../images/3.png" width="80px" alt="ຮູບກາຊາດ">
            <div class="invoice-title">ໃບແຈ້ງໜີ້</div>
            <div class="invoice-number">ເລກທີ: <?= $row['invoice_number'] ?></div>
            <div class="invoice-date">ວັນທີ່ອອກ: <?= date('d/m/Y', strtotime($row['invoice_date'])) ?></div>
            <div class="invoice-date">ກຳນົດຊຳລະ: <?= date('d/m/Y', strtotime($row['due_date'])) ?></div>
        </div>

        <div class="row">
            <div class="col-6">
                <strong>ຂໍ້ມູນຜູ້ຊຳລະ:</strong><br>
                ຊື່-ນາມສະກຸນ: <?= $row['Stu_name'] ?><br>
                ລະຫັດນັກສຶກສາ: <?= $row['Stu_id'] ?><br>
                ເບີໂທ: <?= $row['tell'] ?>
            </div>
            <div class="col-6 text-right">
                <strong>ຂໍ້ມູນຫ້ອງ:</strong><br>
                ຫ້ອງເບີ: <?= $row['R_number'] ?><br>
                ຕຶກ: <?= $row['Build'] ?><br>
                ງວດທີ່: <?= $row['period'] ?><br>
                ສະຖານະ: <span class="<?= $row['status'] == 'paid' ? 'text-success' : 'text-danger' ?>">
                    <?= $row['status'] == 'paid' ? 'ຊຳລະແລ້ວ' : 'ຍັງບໍ່ຊຳລະ' ?>
                </span>
            </div>
        </div>

        <table class="invoice-table mt-3">
            <thead>
                <tr>
                    <th width="10%">ລຳດັບ</th>
                    <th>ລາຍການ</th>
                    <th width="20%">ຈຳນວນເງິນ</th>
                    <th width="20%">ໝາຍເຫດ</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="text-center">1</td>
                    <td>ຄ່າເຊົ່າຫ້ອງພັກ ຕຶກ <?= $row['Build'] ?> ຫ້ອງ <?= $row['R_number'] ?> ງວດ <?= $row['period'] ?></td>
                    <td class="text-right"><?= number_format($row['Price'], 2) ?></td>
                    <td><?= $row['note'] ?></td>
                </tr>
                <tr>
                    <td colspan="2" class="text-right"><strong>ລວມທັງໝົດ</strong></td>
                    <td class="text-right"><strong><?= number_format($row['amount'], 2) ?></strong></td>
                    <td></td>
                </tr>
            </tbody>
        </table>

        <div class="payment-info">
            <h5>ວິທີການຊຳລະ</h5>
            <p>1. ຊຳລະເງິນສົດຢູ່ຫ້ອງການ ວິທະຍາໄລ ລາວວຽງ</p>
            <p>2. ໂອນຜ່ານທະນາຄານ:</p>
            <ul>
                <li>ທະນາຄານ: ທະນາຄານການຄ້າ</li>
                <li>ຊື່ບັນຊີ: ABC</li>
                <li>ເລກບັນຊີ: 123-321-1110</li>
            </ul>
            <p class="text-danger">ໝາຍເຫດ: ກາລຸນາຊຳລະເງິນພາຍໃນກຳນົດ ມິຄ່າປັບໄໝຖ້າລ່າຊ້າ</p>
        </div>

        <div class="invoice-footer">
            <p>ຂອບໃຈທີ່ເລືອກພັກກັບພວກເຮົາ</p>
        </div>
    </div>
</body>
</html>
