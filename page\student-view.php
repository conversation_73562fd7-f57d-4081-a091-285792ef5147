<?php
include_once 'connect-db.php';
$id = $_POST['id'];
$output = '';
$sql = "SELECT s.id as student_id,s.code_stu, s.name as Stu_name, s.gender, s.date_birth, "
    . " CONCAT(TIMESTAMPDIFF(YEAR, date_birth, curdate()),' ປີ ',MOD(TIMESTAMPDIFF(MONTH,  date_birth, curdate()), 12),' ເດືອນ ',TIMESTAMPDIFF(DAY, DATE_ADD( date_birth, INTERVAL TIMESTAMPDIFF(MONTH,  date_birth, curdate()) MONTH), curdate()),' ວັນ ') AS age, "
    . " s.address,m.name AS major_name, p.path, s.sets,s.Gen,s.tell, s.is_active "
    . " FROM student s JOIN majors m ON s.major = m.id JOIN path p ON s.path=p.id WHERE s.id = '$id'";
$result = mysqli_query($link, $sql);
while ($row = mysqli_fetch_assoc($result)) {

    $output .= '<table>';
    $output .= '<tr><td>ລະຫັດນັກສຶກສາ: </td><td>' . $row['code_stu'] . '</td></tr>';
    $output .= '<tr><td>ຊື່ ແລະ ນາມສະກຸນ: </td><td>' . $row['Stu_name'] . '</td></tr>';
    $output .= '<tr><td>ເພດ: </td><td>' . $row['gender'] == 'M' ? 'ຊາຍ' : 'ຍິງ' . '</td></tr>';
    $output .= '<tr><td>ວັນ, ເດືອນ, ປີເກີດ: </td><td>' . date('d/m/Y', strtotime($row['date_birth'])) . '</td></tr>';
    $output .= '<tr><td>ອາຍຸ: </td><td>' . $row['age'] . '</td></tr>';
    $output .= '<tr><td>ທີ່ຢູ່: </td><td>' . $row['address'] . '</td></tr>';
    $output .= '<tr><td>ສາຂາຮຽນ: </td><td>' . $row['major_name'] . '</td></tr>';
    $output .= '<tr><td>ພາກ: </td><td>' . $row['path'] . '</td></tr>';
    $output .= '<tr><td>ຊຸດຮຽນ: </td><td>' . $row['sets'] . '</td></tr>';
    $output .= '<tr><td>ຮຸ້ນທີ: </td><td>' . $row['Gen'] . '</td></tr>';
    $output .= '<tr><td>ເບີໂທ: </td><td>' . $row['tell'] . '</td></tr>';
    $output .= '<tr><td>ສະຖານະ: </td><td>' . $row['is_active'] == '1' ? 'ພັກຢູ່' : 'ອອກແລ້ວ' . '</td></tr>';
    $output .= '</table>';
}

echo $output;
