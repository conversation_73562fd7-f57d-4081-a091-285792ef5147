<?php
require_once 'login-check.php';
require_once 'connect-db.php';
require_once '../function/function.php';

// Check if student_id is provided
if (!isset($_POST['student_id']) || empty($_POST['student_id'])) {
    echo "ບໍ່ພົບຂໍ້ມູນນັກສຶກສາ";
    exit;
}

$id = data_input($_POST['student_id']);
$output = '';

// Updated SQL query with correct column names
$sql = "SELECT s.code_stu, s.name, s.gender, s.date_birth, "
    . " CONCAT(TIMESTAMPDIFF(YEAR, date_birth, curdate()),' ປີ ',MOD(TIMESTAMPDIFF(MONTH,  date_birth, curdate()), 12),' ເດືອນ ',TIMESTAMPDIFF(DAY, DATE_ADD( date_birth, INTERVAL TIMESTAMPDIFF(MONTH,  date_birth, curdate()) MONTH), curdate()),' ວັນ ') AS age, "
    . " s.address, s.major, s.path, s.sets, s.Gen, s.tell, s.Parent, s.Parent_Tell "
    . " FROM student s WHERE s.id = '$id'";
$result = mysqli_query($link, $sql);

if (!$result) {
    echo "ຜິດພາດໃນການດຶງຂໍ້ມູນ: " . mysqli_error($link);
    exit;
}

if (mysqli_num_rows($result) == 0) {
    echo "ບໍ່ພົບຂໍ້ມູນນັກສຶກສາ";
    exit;
}

while ($row = mysqli_fetch_assoc($result)) {
    $output .= '<table class="table table-bordered">';
    $output .= '<tr><td><strong>ລະຫັດນັກສຶກສາ:</strong></td><td>' . htmlspecialchars($row['code_stu']) . '</td></tr>';
    $output .= '<tr><td><strong>ຊື່ ແລະ ນາມສະກຸນ:</strong></td><td>' . htmlspecialchars($row['name']) . '</td></tr>';
    $output .= '<tr><td><strong>ເພດ:</strong></td><td>' . ($row['gender'] == 'M' ? 'ຊາຍ' : 'ຍິງ') . '</td></tr>';
    $output .= '<tr><td><strong>ວັນ, ເດືອນ, ປີເກີດ:</strong></td><td>' . date('d/m/Y', strtotime($row['date_birth'])) . '</td></tr>';
    $output .= '<tr><td><strong>ອາຍຸ:</strong></td><td>' . $row['age'] . '</td></tr>';
    $output .= '<tr><td><strong>ທີ່ຢູ່:</strong></td><td>' . nl2br(htmlspecialchars($row['address'])) . '</td></tr>';
    $output .= '<tr><td><strong>ຊຸດຮຽນ:</strong></td><td>' . htmlspecialchars($row['sets']) . '</td></tr>';
    $output .= '<tr><td><strong>ຮຸ້ນທີ:</strong></td><td>' . htmlspecialchars($row['Gen']) . '</td></tr>';
    $output .= '<tr><td><strong>ເບີໂທ:</strong></td><td>' . htmlspecialchars($row['tell']) . '</td></tr>';

    if (!empty($row['Parent'])) {
        $output .= '<tr><td><strong>ຊື່ຜູ້ປົກຄອງ:</strong></td><td>' . htmlspecialchars($row['Parent']) . '</td></tr>';
    }
    if (!empty($row['Parent_Tell'])) {
        $output .= '<tr><td><strong>ເບີໂທຜູ້ປົກຄອງ:</strong></td><td>' . htmlspecialchars($row['Parent_Tell']) . '</td></tr>';
    }

    $output .= '</table>';
}

echo $output;
