<?php
include_once '../login-check.php';
require_once '../../function/function.php';
require_once '../connect-db.php';

// กำหนดค่าเริ่มต้นสำหรับการกรอง
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // วันแรกของเดือนปัจจุบัน
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // วันสุดท้ายของเดือนปัจจุบัน
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$building = isset($_GET['building']) ? $_GET['building'] : 'all';

// สร้าง SQL query พื้นฐาน
$sql = "SELECT i.*, s.Stu_name, s.tell, r.R_number, r.Build, r.Price
        FROM invoices i
        JOIN student s ON i.Stu_id = s.Stu_ID
        JOIN room r ON i.room_id = r.Room_ID
        WHERE i.invoice_date BETWEEN '$start_date' AND '$end_date'";

// เพิ่มเงื่อนไขการกรอง
if ($status != 'all') {
    $sql .= " AND i.status = '$status'";
}

if ($building != 'all') {
    $sql .= " AND r.Build = '$building'";
}

$sql .= " ORDER BY i.invoice_date DESC, i.id DESC";

$result = mysqli_query($link, $sql);

// คำนวณสรุปยอด
$total_amount = 0;
$total_paid = 0;
$total_unpaid = 0;
$total_overdue = 0;

$temp_result = mysqli_query($link, $sql);
while ($row = mysqli_fetch_assoc($temp_result)) {
    $total_amount += $row['amount'];
    
    if ($row['status'] == 'paid') {
        $total_paid += $row['amount'];
    } else if ($row['status'] == 'unpaid') {
        $total_unpaid += $row['amount'];
        
        // ตรวจสอบว่าเกินกำหนดหรือไม่
        if (strtotime($row['due_date']) < strtotime(date('Y-m-d'))) {
            $total_overdue += $row['amount'];
        }
    }
}

// ดึงรายชื่ออาคาร
$sql_buildings = "SELECT DISTINCT Build FROM room ORDER BY Build";
$result_buildings = mysqli_query($link, $sql_buildings);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ລາຍງານໃບແຈ້ງໜີ້</title>
   <link rel="icon" href="../../images/icon_logo.jpg">

   <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/mystyle.css">
    <link rel="stylesheet" href="../../fontawesome/css/all.min.css">

    <script src="../../js/bootstrap.bundle.min.js"></script>
    <script src="../../js/scripts.js"></script>

    <script src="../../js/sweetalert.min.js"></script>
    <!-- datatable -->
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/jquery.dataTables.min.js"></script>
    <script src="../../js/dataTables.bootstrap5.min.js"></script>

    <script src="../../js/jquery.priceformat.min.js"></script>
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .summary-box {
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .summary-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .summary-value {
            font-size: 24px;
            font-weight: bold;
        }
        .paid {
            color: #28a745;
        }
        .unpaid {
            color: #ffc107;
        }
        .overdue {
            color: #dc3545;
        }
        @media print {
            .no-print {
                display: none;
            }
            .container-fluid {
                width: 100%;
            }
        }
    </style>
</head>
<body class="sb-nav-fixed">
    <!-- ດຶງເມນູເຂົ້າມາ  -->
    <?php include_once '../menu.php' ?>
    <div id="layoutSidenav_content">
    <div class="container-fluid mt-2">
        <div class="row">
            <div class="col-12">
                <h3 class="mb-3">ລາຍງານໃບແຈ້ງໜີ້</h3>
                
                <div class="filter-section no-print">
                    <form method="get" class="row">
                        <div class="col-md-3 mb-2">
                            <label for="start_date">ວັນທີ່ເລີ່ມຕົ້ນ</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="end_date">ວັນທີ່ສີ້ນສຸດ</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-2 mb-2">
                            <label for="status">ສະຖານະ</label>
                            <select name="status" id="status" class="form-control">
                                <option value="all" <?= $status == 'all' ? 'selected' : '' ?>>ທັງໝົດ</option>
                                <option value="paid" <?= $status == 'paid' ? 'selected' : '' ?>>ຊຳລະແລ້ວ</option>
                                <option value="unpaid" <?= $status == 'unpaid' ? 'selected' : '' ?>>ຍັງບໍ່ຊຳລະ</option>
                                <option value="overdue" <?= $status == 'overdue' ? 'selected' : '' ?>>ເກີນກຳໜົດ</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-2">
                            <label for="building">ຕຶກ</label>
                            <select name="building" id="building" class="form-control">
                                <option value="all">ທັງໝົດ</option>
                                <?php while ($row_building = mysqli_fetch_assoc($result_buildings)) { ?>
                                    <option value="<?= $row_building['Build'] ?>" <?= $building == $row_building['Build'] ? 'selected' : '' ?>>
                                        ຕຶກ <?= $row_building['Build'] ?>
                                    </option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-2 mb-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">ຄົ້ນຫາ</button>
                            <button type="button" class="btn btn-success ml-2" onclick="window.print()">ພິມລາຍງານ</button>
                        </div>
                    </form>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="summary-box">
                            <div class="summary-title">ຍອດລວມທັງໝົດ</div>
                            <div class="summary-value"><?= number_format($total_amount, 2) ?> ກີບ</div>
                            <div>ຈຳນວນ <?= mysqli_num_rows($temp_result) ?> ລາຍການ</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-box">
                            <div class="summary-title">ຊຳລະແລ້ວ</div>
                            <div class="summary-value paid"><?= number_format($total_paid, 2) ?> ກີບ</div>
                            <div>ຄິດເປັນ <?= $total_amount > 0 ? number_format(($total_paid / $total_amount) * 100, 2) : 0 ?>%</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-box">
                            <div class="summary-title">ຍັງບໍ່ຊຳລະ</div>
                            <div class="summary-value unpaid"><?= number_format($total_unpaid, 2) ?> ກີບ</div>
                            <div>ຄິດເປັນ <?= $total_amount > 0 ? number_format(($total_unpaid / $total_amount) * 100, 2) : 0 ?>%</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-box">
                            <div class="summary-title">ເກີນກຳໜົດ</div>
                            <div class="summary-value overdue"><?= number_format($total_overdue, 2) ?> ກີບ</div>
                            <div>ຄິດເປັນ <?= $total_amount > 0 ? number_format(($total_overdue / $total_amount) * 100, 2) : 0 ?>%</div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table id="invoiceTable" class="table table-striped table-bordered">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>ລຳດັບ</th>
                                <th>ເລກໃບແຈ້ງໜີ້</th>
                                <th>ວັນທີ່ອອກ</th>
                                <th>ກຳນົດຊຳລະ</th>
                                <th>ນັກສຶກສາ</th>
                                <th>ຫ້ອງ</th>
                                <th>ງວດທີ່</th>
                                <th>ຈຳນວນເງີນ</th>
                                <th>ສະຖານະ</th>
                                <th class="no-print">ຕົວເລຶອກ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $number = 1;
                            while ($row = mysqli_fetch_assoc($result)) {
                                // ตรวจสอบว่าเกินกำหนดหรือไม่
                                $is_overdue = ($row['status'] == 'unpaid' && strtotime($row['due_date']) < strtotime(date('Y-m-d')));
                                
                                // กำหนดคลาสสำหรับแถวตามสถานะ
                                $row_class = '';
                                if ($row['status'] == 'paid') {
                                    $row_class = 'table-success';
                                } else if ($is_overdue) {
                                    $row_class = 'table-danger';
                                } else if ($row['status'] == 'unpaid') {
                                    $row_class = 'table-warning';
                                }
                            ?>
                                <tr class="<?= $row_class ?>">
                                    <td><?= $number++ ?></td>
                                    <td><?= $row['invoice_number'] ?></td>
                                    <td><?= date('d/m/Y', strtotime($row['invoice_date'])) ?></td>
                                    <td><?= date('d/m/Y', strtotime($row['due_date'])) ?></td>
                                    <td><?= $row['Stu_name'] ?></td>
                                    <td>อาคาร <?= $row['Build'] ?> ห้อง <?= $row['R_number'] ?></td>
                                    <td><?= $row['period'] ?></td>
                                    <td class="text-right"><?= number_format($row['amount'], 2) ?></td>
                                    <td>
                                        <?php if ($row['status'] == 'paid') { ?>
                                            <span class="badge badge-success">ຊຳລະແລ້ວ</span>
                                        <?php } else if ($is_overdue) { ?>
                                            <span class="badge badge-danger">ເກີນກຳໜົດ</span>
                                        <?php } else { ?>
                                            <span class="badge badge-warning">ຍັງບໍ່ຊຳລະ</span>
                                        <?php } ?>
                                    </td>
                                    <td class="no-print">
                                        <a href="invoice-print.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" target="_blank">
                                            <i class="fas fa-print"></i> ພິມ
                                        </a>
                                        <?php if ($row['status'] == 'unpaid') { ?>
                                            <a href="../payment.php?invoice_id=<?= $row['id'] ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-money-bill-wave"></i> ຊຳລະເງີນ
                                            </a>
                                        <?php } ?>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <?php include_once '../footer.php'; ?>

    <script>
        $(document).ready(function() {
            $('#invoiceTable').DataTable({
                "pageLength": 25,
                "order": [[2, "desc"]],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/lao.json"
                }
            });
        });
    </script>
    </div>
</body>
</html>