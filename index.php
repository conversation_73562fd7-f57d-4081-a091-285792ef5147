<?php
session_start();
require_once 'page/connect-db.php';

$username =  $_SESSION['username'];
$password = $_SESSION['password'];

$sql = "SELECT *FROM users WHERE username = '$username' AND password='$password'";
$result = mysqli_query($link, $sql);
if(mysqli_num_rows($result) <= 0) {
    header('location: page/login-form.php');
}
    require_once 'function/function.php';

    // ດຶງຂໍ້ມູນສະຖິຕິຕ່າງໆ
    // 1. ຈຳນວນນັກສຶກສາທັງໝົດ
    $sql_students    = "SELECT COUNT(*) as total FROM student";
    $result_students = mysqli_query($link, $sql_students);
    $total_students  = mysqli_fetch_assoc($result_students)['total'];

    // 2. ຈຳນວນຫ້ອງທັງໝົດ
    $sql_rooms    = "SELECT COUNT(*) as total FROM room";
    $result_rooms = mysqli_query($link, $sql_rooms);
    $total_rooms  = mysqli_fetch_assoc($result_rooms)['total'];

    // 3. ຈຳນວນນັກສຶກສາທີ່ກຳລັງພັກຢູ່
    // $sql_active_students = "SELECT COUNT(*) as total FROM room_members WHERE is_active = 1";
    // $result_active       = mysqli_query($link, $sql_active_students);
    // $active_students     = mysqli_fetch_assoc($result_active)['total'];

    // 4. ຈຳນວນໜີ້ຄ້າງຊຳລະ
    // $sql_debts    = "SELECT COUNT(*) as total FROM invoices WHERE status = 'ຍັງບໍ່ຊຳລະ'";
    // $result_debts = mysqli_query($link, $sql_debts);
    // $total_debts  = mysqli_fetch_assoc($result_debts)['total'];

    // 5. ຍອດໜີ້ຄ້າງຊຳລະທັງໝົດ
//     // $sql_debt_amount = "SELECT SUM(i.amount - p.amount_paid) as total FROM invoices join payments p on i.id = p.invoice_id WHERE status = 'ຍັງບໍ່ຊຳລະ'";
//     $sql_debt_amount = "
//   SELECT SUM(i.amount - IFNULL(p.amount_paid, 0)) as total
//   FROM invoices i
//   LEFT JOIN payments p ON i.id = p.invoice_id
//   WHERE i.status = 'ຍັງບໍ່ຊຳລະ'
// ";
//     $result_debt_amount = mysqli_query($link, $sql_debt_amount);
//     $total_debt_amount  = mysqli_fetch_assoc($result_debt_amount)['total'] ?? 0;

//     // ຂໍ້ມູນສຳລັບກຣາຟວົງກົມສະແດງອັດຕາການເຂົ້າພັກ
//     $sql_total = "SELECT
//         SUM(TotalCapacity) AS total_capacity,
//         SUM(IFNULL(Persons, 0)) AS total_occupied
//     FROM room";
//     $result_total = mysqli_query($link, $sql_total);
//     $data_total   = mysqli_fetch_assoc($result_total);

//     $occupied_percent  = $data_total['total_occupied'];
//     $available_percent = ($data_total['total_capacity'] - $data_total['total_occupied']);

//     // ຂໍ້ມູນສຳລັບກຣາຟແທ່ງສະແດງຈຳນວນນັກສຶກສາແຍກຕາມຕຶກ
//     $sql_building = "SELECT
//         r.Build,
//         COUNT(rm.id) as student_count
//     FROM room r
//     LEFT JOIN room_members rm ON r.Room_ID = rm.room_id AND rm.is_active = 1
//     GROUP BY r.Build
//     ORDER BY r.Build";
//     $result_building = mysqli_query($link, $sql_building);

//     $building_labels = [];
//     $building_data   = [];

//     while ($row = mysqli_fetch_assoc($result_building)) {
//         $building_labels[] = 'ຕຶກ ' . $row['Build'];
//         $building_data[]   = (int) $row['student_count'];
//     }

//     // ຂໍ້ມູນສຳລັບກຣາຟເສັ້ນສະແດງການຊຳລະໜີ້ຍ້ອນຫລັງ 6 ເດືອນ
//     $sql_payments = "SELECT
//         DATE_FORMAT(paid_at, '%m/%Y') as month_year,
//         SUM(amount_paid) as total_amount
//     FROM payments
//     WHERE paid_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
//     GROUP BY DATE_FORMAT(paid_at, '%Y-%m')
//     ORDER BY paid_at";
//     $result_payments = mysqli_query($link, $sql_payments);

//     $payment_labels = [];
//     $payment_data   = [];

//     while ($row = mysqli_fetch_assoc($result_payments)) {
//         $payment_labels[] = $row['month_year'];
//         $payment_data[]   = (int) $row['total_amount'];
//     }
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />

    <title>ລະບົບຈັດການຫໍພັກ - Dashboard</title>
    <link rel="icon" href="images/icon_logo.jpg">

    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/dataTables.bootstrap5.min.css">
    <link href="css/styles.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/mystyle.css">
    <link rel="stylesheet" href="fontawesome/css/all.min.css">

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/scripts.js"></script>

    <!-- datatable -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <script src="js/dataTables.bootstrap5.min.js"></script>

    <!-- Chart.js -->
    <script src="js/Chart.min.js"></script>
</head>

<body class="sb-nav-fixed">
    <!-- ດຶງເມນູເຂົ້າມາ  -->
    <?php include_once 'page/menu.php'?>

    <div id="layoutSidenav_content">
        <main>
            <div class="container-fluid px-4">
                <h1 class="mt-4">Dashboard</h1>
                <ol class="breadcrumb mb-4">
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>

                <!-- ແຖບສະຖິຕິ -->
                <div class="row">
                    <div class="col-xl-3 col-md-6">
                        <div class="card bg-primary text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($total_students); ?></h5>
                                        <div>ນັກສຶກສາທັງໝົດ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-users fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="page/student-management.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <div class="card bg-success text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($active_students); ?></h5>
                                        <div>ນັກສຶກສາທີ່ກຳລັງພັກຢູ່</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-bed fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="page/room-members.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <div class="card bg-warning text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($total_rooms); ?></h5>
                                        <div>ຫ້ອງທັງໝົດ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-door-open fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="page/room.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <div class="card bg-danger text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($total_debts); ?></h5>
                                        <div>ໜີ້ຄ້າງຊຳລະ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-money-bill-wave fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="invoice/invoice-report.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ແຖບສະຖິຕິເພີ່ມເຕີມ -->
                <div class="row mb-4">
                    <div class="col-xl-6 col-md-6">
                        <div class="card bg-info text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($total_debt_amount); ?> ກີບ</h5>
                                        <div>ຍອດໜີ້ຄ້າງຊຳລະທັງໝົດ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-file-invoice-dollar fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="invoice/invoice-report.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6 col-md-6">
                        <div class="card bg-secondary text-white mb-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0"><?php echo number_format($available_percent); ?> ຕຽງ</h5>
                                        <div>ຕຽງວ່າງທັງໝົດ</div>
                                    </div>
                                    <div>
                                        <i class="fas fa-bed fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a class="small text-white stretched-link" href="room.php">ເບິ່ງລາຍລະອຽດ</a>
                                <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ກຣາຟ -->
                <div class="row">
                    <div class="col-xl-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="fas fa-chart-pie me-1"></i>
                                ອັດຕາການເຂົ້າພັກຂອງຫ້ອງ
                            </div>
                            <div class="card-body">
                                <canvas id="occupancyChart" width="100%" height="40"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="fas fa-chart-bar me-1"></i>
                                ຈຳນວນນັກສຶກສາແຍກຕາມຕຶກ
                            </div>
                            <div class="card-body">
                                <canvas id="buildingChart" width="100%" height="40"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ກຣາຟເພີ່ມເຕີມ -->
                <div class="row">
                    <div class="col-xl-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="fas fa-chart-line me-1"></i>
                                ການຊຳລະໜີ້ຍ້ອນຫລັງ 6 ເດືອນ
                            </div>
                            <div class="card-body">
                                <canvas id="paymentChart" width="100%" height="30"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ຕາຕະລາງຂໍ້ມູນໜີ້ຄ້າງຊຳລະລ່າສຸດ -->
                <div class="row">
                    <div class="col-xl-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="fas fa-table me-1"></i>
                                ໜີ້ຄ້າງຊຳລະລ່າສຸດ
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-hover">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>ລະຫັດໜີ້</th>
                                                <th>ຊື່ນັກສຶກສາ</th>
                                                <th>ລາຍລະອຽດໜີ້</th>
                                                <th>ຈຳນວນເງິນ</th>
                                                <th>ວັນທີຄົບກຳນົດ</th>
                                                <th>ສະຖານະ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                                $sql_recent_debts = "SELECT d.*, s.Stu_name
                                                                FROM invoices d
                                                                JOIN student s ON d.Stu_ID = s.Stu_ID
                                                                WHERE d.status = 'ຍັງບໍ່ຊຳລະ'
                                                                ORDER BY d.due_date ASC
                                                                LIMIT 5";
                                                $result_recent_debts = mysqli_query($link, $sql_recent_debts);

                                                if ($result_recent_debts && mysqli_num_rows($result_recent_debts) > 0) {
                                                    while ($row = mysqli_fetch_assoc($result_recent_debts)) {
                                                        $remaining    = $row['amount'] - $row['amount_paid'];
                                                        $due_date     = new DateTime($row['due_date']);
                                                        $today        = new DateTime();
                                                        $status_class = ($due_date < $today) ? 'text-danger' : 'text-warning';
                                                    ?>
                                            <tr>
                                                <td><?php echo $row['id']; ?></td>
                                                <td><?php echo $row['Stu_name']; ?></td>
                                                <td><?php echo $row['description']; ?></td>
                                                <td class="text-end"><?php echo number_format($remaining, 0, ',', '.'); ?> ກີບ</td>
                                                <td><?php echo date('d/m/Y', strtotime($row['due_date'])); ?></td>
                                                <td class="<?php echo $status_class; ?>"><?php echo $row['status']; ?></td>
                                            </tr>
                                            <?php
                                                }
                                                } else {
                                                ?>
                                            <tr>
                                                <td colspan="6" class="text-center">ບໍ່ພົບຂໍ້ມູນໜີ້ຄ້າງຊຳລະ</td>
                                            </tr>
                                            <?php
                                                }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-2">
                                    <a href="page/invoice/invoice-report.php" class="btn btn-primary btn-sm">ເບິ່ງທັງໝົດ</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <!-- footer -->
        <?php include_once 'page/footer.php'?>
    </div>

    <script>
    $(document).ready(function() {
        // ກຣາຟວົງກົມສະແດງອັດຕາການເຂົ້າພັກ
        var occupancyCtx = document.getElementById('occupancyChart').getContext('2d');
        var occupancyChart = new Chart(occupancyCtx, {
            type: 'pie',
            data: {
                labels: ['ຕຽງວ່າງ', 'ຕຽງບໍ່ວ່າງ'],
                datasets: [{
                    data: [<?php echo $available_percent; ?>,<?php echo $occupied_percent; ?>],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(220, 53, 69, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                legend: {
                    position: 'bottom',
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            var dataset = data.datasets[tooltipItem.datasetIndex];
                            var total = dataset.data.reduce(function(previousValue, currentValue) {
                                return previousValue + currentValue;
                            });
                            var currentValue = dataset.data[tooltipItem.index];
                            var percentage = Math.floor(((currentValue/total) * 100)+0.5);
                            return data.labels[tooltipItem.index] + ': ' + currentValue + ' ຕຽງ (' + percentage + "%)";
                        }
                    }
                }
            }
        });

        // ກຣາຟແທ່ງສະແດງຈຳນວນນັກສຶກສາແຍກຕາມຕຶກ
        var buildingCtx = document.getElementById('buildingChart').getContext('2d');
        var buildingChart = new Chart(buildingCtx, {
            type: 'bar',
            data: {
                labels:                        <?php echo json_encode($building_labels); ?>,
                datasets: [{
                    label: 'ຈຳນວນນັກສຶກສາ',
                    data:                          <?php echo json_encode($building_data); ?>,
                    backgroundColor: 'rgba(0, 123, 255, 0.5)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            callback: function(value) {if (value % 1 === 0) {return value;}}
                        }
                    }]
                }
            }
        });

        // ກຣາຟເສັ້ນສະແດງການຊຳລະໜີ້ຍ້ອນຫລັງ 6 ເດືອນ
        var paymentCtx = document.getElementById('paymentChart').getContext('2d');
        var paymentChart = new Chart(paymentCtx, {
            type: 'line',
            data: {
                labels:                        <?php echo json_encode($payment_labels); ?>,
                datasets: [{
                    label: 'ຍອດຊຳລະໜີ້ (ກີບ)',
                    data:                          <?php echo json_encode($payment_data); ?>,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(75, 192, 192, 1)',
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            callback: function(value) {
                                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ' ກີບ';
                            }
                        }
                    }]
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return data.datasets[tooltipItem.datasetIndex].label + ': ' +
                                tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ' ກີບ';
                        }
                    }
                }
            }
        });
    });
    </script>
</body>
</html>

