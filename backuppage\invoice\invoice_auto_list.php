<?php
require_once '../connect-db.php';

// ดึงใบแจ้งหนี้ที่สร้างอัตโนมัติ (period > 1) และยังไม่ได้จ่าย
$sql = "
SELECT i.*, s.Stu_name, r.R_number, r.Build
FROM invoices i
JOIN student s ON i.Stu_id = s.Stu_ID
JOIN room r ON i.room_id = r.Room_ID
WHERE i.status = 'unpaid' AND i.period > 1
ORDER BY i.invoice_date DESC
";
$result = mysqli_query($link, $sql);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>📄 ใบแจ้งหนี้ที่สร้างอัตโนมัติ</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#invoiceTable').DataTable();
        });
    </script>
</head>
<body class="p-4 bg-light">
    <div class="container">
        <h3 class="mb-4">📄 รายการใบแจ้งหนี้ที่สร้างโดยระบบ</h3>
        <table id="invoiceTable" class="table table-bordered table-striped table-hover">
            <thead class="table-dark text-center">
                <tr>
                    <th>เลขที่ใบแจ้งหนี้</th>
                    <th>นักศึกษา</th>
                    <th>ห้อง</th>
                    <th>วันที่ออก</th>
                    <th>ครบกำหนด</th>
                    <th>จำนวนเงิน</th>
                    <th>งวดที่</th>
                    <th>สถานะ</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = mysqli_fetch_assoc($result)): ?>
                    <tr>
                        <td><?= $row['invoice_number'] ?></td>
                        <td><?= $row['Stu_name'] ?></td>
                        <td>ตึก <?= $row['Build'] ?> ห้อง <?= $row['R_number'] ?></td>
                        <td><?= date('d/m/Y', strtotime($row['invoice_date'])) ?></td>
                        <td><?= date('d/m/Y', strtotime($row['due_date'])) ?></td>
                        <td class="text-end"><?= number_format($row['amount'], 0) ?> ₭</td>
                        <td class="text-center"><?= $row['period'] ?></td>
                        <td class="text-center">
                            <span class="badge bg-danger">ยังไม่จ่าย</span>
                        </td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
