<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// ดึงข้อมูลการชำระเงินที่ใกล้ครบกำหนดหรือเลยกำหนดแล้ว
$today = date('Y-m-d');
$warning_days = 7; // แจ้งเตือนล่วงหน้า 7 วัน

$sql = "SELECT p.id, p.Stu_id, p.room_id, p.next_payment_date, p.amount, p.payment_date,
               s.Stu_name, s.tell, r.R_number, r.Build, r.Price,
               DATEDIFF(p.next_payment_date, CURDATE()) as days_remaining,
               CASE 
                   WHEN p.next_payment_date < CURDATE() THEN 'overdue'
                   WHEN DATEDIFF(p.next_payment_date, CURDATE()) <= $warning_days THEN 'warning'
                   ELSE 'normal'
               END as status_type
        FROM payments p
        JOIN student s ON p.Stu_id = s.Stu_ID
        JOIN room r ON p.room_id = r.Room_ID
        WHERE p.next_payment_date IS NOT NULL
        AND (p.next_payment_date <= DATE_ADD(CURDATE(), INTERVAL $warning_days DAY)
             OR p.next_payment_date < CURDATE())
        ORDER BY p.next_payment_date ASC";

$result = mysqli_query($link, $sql);
$payment_due_data = [];
if ($result) {
    $payment_due_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
}

// นับจำนวนตามสถานะ
$overdue_count = 0;
$warning_count = 0;
foreach ($payment_due_data as $row) {
    if ($row['status_type'] == 'overdue') {
        $overdue_count++;
    } elseif ($row['status_type'] == 'warning') {
        $warning_count++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ລະບົບຈັດການຫໍພັກ - ແຈ້ງເຕືອນການຊຳລະເງິນ</title>

    <link rel="icon" href="../images/3.png">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>
    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <style>
        .overdue-row {
            background-color: #ffebee !important;
            border-left: 4px solid #f44336;
        }
        .warning-row {
            background-color: #fff3e0 !important;
            border-left: 4px solid #ff9800;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .stats-card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>

<body class="sb-nav-fixed">
    <?php include_once 'menu.php' ?>
    <div id="layoutSidenav_content">
        <div class="container-fluid mt-2">
            <!-- สถิติการแจ้งเตือน -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card stats-card border-danger">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger me-3"></i>
                                <div>
                                    <h3 class="text-danger mb-0"><?= $overdue_count ?></h3>
                                    <p class="mb-0">ເລຍກຳໜົດແລ້ວ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card stats-card border-warning">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="fas fa-clock fa-2x text-warning me-3"></i>
                                <div>
                                    <h3 class="text-warning mb-0"><?= $warning_count ?></h3>
                                    <p class="mb-0">ໃກ້ຄົບກຳໜົດ (7 ວັນ)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ตาราง -->
            <div class="row">
                <div class="col-12">
                    <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">
                            <i class="fas fa-bell"></i> ແຈ້ງເຕືອນການຊຳລະເງິນ
                        </legend>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ຄຳອະທິບາຍ:</strong>
                            <span class="badge bg-danger ms-2">ເລຍກຳໜົດ</span> = ເລຍວັນທີກຳໜົດຊຳລະແລ້ວ |
                            <span class="badge bg-warning ms-2">ໃກ້ຄົບກຳໜົດ</span> = ເຫຼືອ 7 ວັນ ຫຼື ໜ້ອຍກວ່າ
                        </div>

                        <table id="paymentDueTable" class="table table-striped" style="width:100%">
                            <thead class="bg-secondary text-center text-white">
                                <tr>
                                    <th>ລຳດັບ</th>
                                    <th>ສະຖານະ</th>
                                    <th>ຊື່ນັກສຶກສາ</th>
                                    <th>ເບີໂທ</th>
                                    <th>ຫ້ອງພັກ</th>
                                    <th>ວັນທີຊຳລະຄັ້ງສຸດທ້າຍ</th>
                                    <th>ວັນທີຄົບກຳໜົດຊຳລະ</th>
                                    <th>ວັນທີເຫຼືອ</th>
                                    <th>ຈຳນວນເງິນ</th>
                                    <th>ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($payment_due_data)): ?>
                                    <?php foreach ($payment_due_data as $i => $row): ?>
                                        <tr class="<?= $row['status_type'] == 'overdue' ? 'overdue-row' : ($row['status_type'] == 'warning' ? 'warning-row' : '') ?>">
                                            <td class="text-center"><?= $i + 1 ?></td>
                                            <td class="text-center">
                                                <?php if ($row['status_type'] == 'overdue'): ?>
                                                    <span class="badge bg-danger status-badge">
                                                        <i class="fas fa-exclamation-triangle"></i> ເລຍກຳໜົດ
                                                    </span>
                                                <?php elseif ($row['status_type'] == 'warning'): ?>
                                                    <span class="badge bg-warning status-badge">
                                                        <i class="fas fa-clock"></i> ໃກ້ຄົບກຳໜົດ
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $row['Stu_name'] ?></td>
                                            <td><?= $row['tell'] ?></td>
                                            <td>ຕຶກ <?= $row['Build'] ?> ຫ້ອງ <?= $row['R_number'] ?></td>
                                            <td><?= date('d/m/Y', strtotime($row['payment_date'])) ?></td>
                                            <td><?= date('d/m/Y', strtotime($row['next_payment_date'])) ?></td>
                                            <td class="text-center">
                                                <?php if ($row['days_remaining'] < 0): ?>
                                                    <span class="text-danger fw-bold">
                                                        ເລຍມາ <?= abs($row['days_remaining']) ?> ວັນ
                                                    </span>
                                                <?php elseif ($row['days_remaining'] == 0): ?>
                                                    <span class="text-warning fw-bold">ວັນນີ້</span>
                                                <?php else: ?>
                                                    <span class="text-info">ເຫຼືອ <?= $row['days_remaining'] ?> ວັນ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-end"><?= number_format($row['Price'], 2) ?> ກີບ</td>
                                            <td class="text-center">
                                                <a href="paymentions.php?student_id=<?= $row['Stu_id'] ?>" class="btn btn-success btn-sm">
                                                    <i class="fas fa-money-bill-wave"></i> ຊຳລະເງິນ
                                                </a>
                                                <a href="tel:<?= $row['tell'] ?>" class="btn btn-info btn-sm">
                                                    <i class="fas fa-phone"></i> ໂທ
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="alert alert-success mb-0">
                                                <i class="fas fa-check-circle"></i>
                                                ບໍ່ມີການຊຳລະເງິນທີ່ໃກ້ຄົບກຳໜົດ ຫຼື ເລຍກຳໜົດ
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </fieldset>
                </div>
            </div>
        </div>

        <?php include_once 'footer.php' ?>
    </div>
</body>

<script>
$(document).ready(function() {
    $('#paymentDueTable').DataTable({
        "language": {
            "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
            "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
            "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
            "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
            "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
            "search": "ຄົ້ນຫາ:",
            "paginate": {
                "first": "ໜ້າທຳອິດ",
                "last": "ໜ້າສຸດທ້າຍ",
                "next": "ໜ້າຕໍ່ໄປ",
                "previous": "ໜ້າກ່ອນ"
            },
            "processing": "ກຳລັງປະມວນຜົນ...",
            "loadingRecords": "ກຳລັງໂຫຼດ...",
            "emptyTable": "ບໍ່ມີຂໍ້ມູນໃນຕາຕະລາງ"
        },
        "pageLength": 25,
        "order": [[6, "asc"]], // เรียงตามวันที่ครบกำหนด (เร็วที่สุดก่อน)
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": [9] } // ปิดการเรียงลำดับคอลัมน์ "จัดการ"
        ]
    });
});
</script>

</html>
