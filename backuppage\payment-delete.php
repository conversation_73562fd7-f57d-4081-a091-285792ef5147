<?php
include_once 'login-check.php';
require_once '../function/function.php';

// ตรวจสอบว่าเป็น admin หรือไม่
if ($_SESSION['role'] != 'admin') {
    echo "ທ່ານບໍ່ມີສິດໃນການລຶບຂໍ້ມູນ";
    exit;
}
$userid = $_SESSION['user_id'];
if (isset($_POST['payment_id']) && isset($_POST['receipt_id'])) {
    $payment_id = data_input($_POST['payment_id']);
    $receipt_id = data_input($_POST['receipt_id']);
    
    // เışı่ม transaction
    mysqli_begin_transaction($link);
    
    try {
        // 1. ข้อมูลการชำระเพื่อเอา invoice_id
        $sql = "SELECT invoice_id, amount_paid FROM payments WHERE id = '$payment_id'";
        $result = mysqli_query($link, $sql);
        
        if (!$result || mysqli_num_rows($result) == 0) {
            throw new Exception("ບໍ່ພົບຂໍ້ມູນການຊຳລະເງິນ");
        }
        
        $payment_data = mysqli_fetch_assoc($result);
        $invoice_id = $payment_data['invoice_id'];
        $amount_paid = $payment_data['amount_paid'];
        
        // 2. <lemmaสถานะใบแจ้ง<lemma้<|im_start|>เป็น unpaid
        $sql = "UPDATE invoices SET status = 'unpaid' WHERE id = '$invoice_id'";
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception(mysqli_error($link));
        }
        
        // 3. ลบข้อมูลใบเสร็จ
        $sql = "DELETE FROM receipts WHERE id = '$receipt_id'";
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception(mysqli_error($link));
        }
        
        // 4. ลบข้อมูลการชำระ<lemma
        $sql = "DELETE FROM payments WHERE id = '$payment_id'";
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception(mysqli_error($link));
        }
        
        // การทำงานลงในตาราง system_logs
        $action = "ລຶບຂໍ້ມູນການຊຳລະເງິນ: ລະຫັດການຊຳລະ $payment_id, ລະຫັດໃບເສັດຮັບເງິນ $receipt_id, ຈຳນວນເງິນ $amount_paid";
        $sql = "INSERT INTO system_logs (user_id, action, action_date) VALUES ('$userid', '$action', NOW())";
        mysqli_query($link, $sql);
        
        // Commit transaction
        mysqli_commit($link);
        
    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($link);
        echo "ຜິດພາດ: " . $e->getMessage();
    }
}
?>
