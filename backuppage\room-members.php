<?php
    // error_reporting(E_ALL);
    // ini_set('display_errors', 1);
    // if (isset($_POST['btnAdd'])) {
    //     echo "Button Pressed";
    //     exit;
    // }

    include_once 'login-check.php';
    require_once '../function/function.php';
    require_once 'connect-db.php';

    // สร้างเลขใบแจ้ง
    function generateInvoiceNumber()
    {
        $datePart = date('Ymd');
        $random   = uniqid();
        return "INV-$datePart-$random";
    }

    // เมื่อกดปุ่มເພີ່ມ
    if (isset($_POST['btnAdd'])) {
        $Stu_id        = data_input($_POST['Stu_id']);
        $room_id       = data_input($_POST['room_id']);
        $start_date    = data_input($_POST['start_date']);
        $qty           = data_input($_POST['qty']);
        $check_in_date = data_input($_POST['check_in_date']);
        $note          = data_input($_POST['note']);

        // ตรวจสอบว่าคนเข้า active อยู่แล้ว
        $sql    = "SELECT * FROM room_members WHERE Stu_id='$Stu_id' AND is_active=1";
        $result = mysqli_query($link, $sql);
        if (mysqli_num_rows($result) > 0) {
            $error_Stu_id = "คนเข้า active อยู่แล้ว";
        } else {
            // เริ่ม transaction
            mysqli_begin_transaction($link);
            try {
                // 1. บันทึกข้อมูลการเข้า
                $member_id = null;
                $sql       = "INSERT INTO room_members (Stu_id, room_id, start_date, qty, check_in_date, is_active, note)
                        VALUES (?, ?, ?, ?, ?, 1, ?)";
                $stmt = mysqli_prepare($link, $sql);
                mysqli_stmt_bind_param($stmt, "ssssss", $Stu_id, $room_id, $start_date, $qty, $check_in_date, $note);
                mysqli_stmt_execute($stmt);
                $member_id = mysqli_insert_id($link);

                if (! $stmt) {
                    throw new Exception(mysqli_error($link));
                }
                // 2. ปรับสถานะห้อง
                $sql_update_room = "UPDATE room SET Persons = Persons + 1 WHERE Room_ID = '$room_id'";
                $result          = mysqli_query($link, $sql_update_room);

                if (! $result) {
                    throw new Exception(mysqli_error($link));
                }

                // 3. ปรับสถานะนักศึกษา
                $sql_update_student = "UPDATE student SET status = 'ເຂົ້າພັກແລ້ວ' WHERE Stu_ID = '$Stu_id'";
                $result             = mysqli_query($link, $sql_update_student);

                if (! $result) {
                    throw new Exception(mysqli_error($link));
                }

                // 4. ดึงข้อมูลราคาห้อง
                $sql_room    = "SELECT Price FROM room WHERE Room_ID = '$room_id'";
                $result_room = mysqli_query($link, $sql_room);
                $room_data   = mysqli_fetch_assoc($result_room);
                $room_price  = $room_data['Price'];

                // 5. สร้างใบแจ้ง
                $invoice_id     = null;
                $invoice_number = generateInvoiceNumber();
                $invoice_date   = date('Y-m-d');
                $due_date       = date('Y-m-d', strtotime('+7 days')); // กำหนดชำระภายใน 7 วัน
                if ($qty == 4) {
                    $period = 1;
                } else if ($qty == 8) {
                    $period = 2;
                } else if ($qty == 12) {
                    $period = 3;
                }
                $amount      = $room_price * $period; // ราคาห้อง
                $sql_invoice = "INSERT INTO invoices (Stu_id, room_id, invoice_number, invoice_date, due_date, amount, status, period)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($link, $sql_invoice);
                if (! $stmt) {
                    throw new Exception("Prepare failed: " . mysqli_error($link));
                }

                $status     = 'unpaid';
                $bindResult = mysqli_stmt_bind_param($stmt, "sssssdsi", $Stu_id, $room_id, $invoice_number, $invoice_date, $due_date, $amount, $status, $period);
                if (! $bindResult) {
                    throw new Exception("Bind failed: " . mysqli_stmt_error($stmt));
                }

                $execResult = mysqli_stmt_execute($stmt);
                if (! $execResult) {
                    throw new Exception("Execute failed: " . mysqli_stmt_error($stmt));
                }

                $invoice_id = mysqli_insert_id($link);

                if (! $stmt) {
                    throw new Exception(mysqli_error($link));
                }

                // Commit transaction
                mysqli_commit($link);

                $message = '<script>swal("ສຳເລັດ", "ຂໍ້ມູນບັນທຶກລົງໃນຖານຂໍ້ມູນແລ້ວ", "success",{button: "ຕົກລົງ"}).then(function() {
                window.location = "invoice/invoice-print.php?id=' . $invoice_id . '";
            });</script>';

                $Stu_id = $room_id = $start_date = $qty = $check_in_date = $note = null;

            } catch (Exception $e) {
                // Rollback transaction
                mysqli_rollback($link);
                echo "ຂໍ້ຜິດພາດ: " . $e->getMessage();
            }
        }
    }

    // เมื่อกดปุ่มลบข้อมูล
    if (isset($_POST['btnDelete'])) {
        $id      = data_input($_POST['id']);
        $Stu_id  = data_input($_POST['Stu_id']);
        $room_id = data_input($_POST['room_id']);

        $sql    = "UPDATE room_members SET is_active = 0, check_out_date = NOW() WHERE id = '$id'";
        $result = mysqli_query($link, $sql);
        if ($result) {
            // ปรับสถานะห้อง
            $sql_update_room = "UPDATE room SET Persons = Persons - 1 WHERE Room_ID = '$room_id'";
            mysqli_query($link, $sql_update_room);

            // ปรับสถานะนักศึกษา
            $sql_update_student = "UPDATE student SET status = 'ອອກພັກແລ້ວ' WHERE Stu_ID = '$Stu_id'";
            mysqli_query($link, $sql_update_student);

            $message = '<script>swal("ສຳເລັດ", "ຍ້າຍຫ້ອງສໍາເລັດແລ້ວ", "success",{button: "ຕົກລົງ"});</script>';
        } else {
            echo mysqli_error($link);
        }
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ລະບົບຈັດການຫໍພັກ - ຂໍ້ມູນການເຂົ້າພັກ</title>
   <link rel="icon" href="../images/icon_logo.jpg">

   <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <!-- datatable -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>
     <!-- CSS & JS ຂອງ Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">
    <style>
    .ts-dropdown .ts-dropdown-content {
        max-height: 200px;
        /* ความสูงสูงสุดของรายการ */
        overflow-y: auto;
        /* แสดง scrollbar เมื่อเกิน */
    }
    </style>
</head>
<body class="sb-nav-fixed">
<?php include_once 'menu.php'?>

    <div id="layoutSidenav_content">
    <div class="container-fluid mt-2">
        <div class="row">
            <div class="col-md-4 mt-4">
                <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                    <legend class="float-none w-auto p-2 h5">ຂໍ້ມູນເຂົ້າພັກ</legend>
                    <form method="post" enctype="multipart/form-data" >
                        <div class="form-group">
                            <label for="Stu_id">ລະຫັດນັກສຶກ</label>
                            <select name="Stu_id" id="Stu_id" class="form-select" required>
                                <option value="">--ເລືອກ--</option>
                                <?php
                                    $sql    = "SELECT Stu_ID, Stu_name FROM student WHERE status = 'ບັນທຶກໃໝ່'";
                                    $result = mysqli_query($link, $sql);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        echo "<option value='{$row['Stu_ID']}'>{$row['Stu_ID']} - {$row['Stu_name']}</option>";
                                    }
                                ?>
                            </select>
                            <div class="text-danger"><?php echo @$error_Stu_id ?></div>
                        </div>

                        <div class="form-group">
                            <label for="room_id">ຫ້ອງ</label>
                            <select name="room_id" id="select-room" class="form-select" required>
                                <option value="">--ເລືອກ--</option>
                                <?php
                                    $sql    = "SELECT Room_ID, R_number, Build, Price FROM room WHERE Persons < TotalCapacity";
                                    $result = mysqli_query($link, $sql);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        echo "<option value='{$row['Room_ID']}'>ຕຶກ {$row['Build']} ຫ້ອງ {$row['R_number']} - ລາຄາ {$row['Price']} ກີບ</option>";
                                    }
                                ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="start_date">ວັນທີ່ເຂົ້າ</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="qty">ຈຳນວນ</label>
                            <input type="number" name="qty" id="qty" class="form-control" min="4" max="12" step="4" required>
                        </div>

                        <div class="form-group">
                            <label for="check_in_date">ວັນທີ່ເຂົ້າ</label>
                            <input type="date" name="check_in_date" id="check_in_date" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="note">ໝາຍເຫດ</label>
                            <textarea name="note" id="note" class="form-control"></textarea>
                        </div>
                        <div class="form-group" style="margin-top: 20px;" >
                              <button type="submit" name="btnAdd" class="btn btn-primary btn-block">ເພີ່ມ</button>
                        </div>

                    </form>
                </fieldset>
            </div>

            <div class="col-md-8 mt-4">
                <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                    <legend class="float-none w-auto p-2 h5">ຂໍ້ມູນການເຂົ້າພັກ</legend>
                    <table id="example" class="table table-striped" style="width:100%">
                        <thead class="bg-secondary text-center text-white">
                            <tr>
                                <th>ລຳດັບ</th>
                                <th>ລະຫັດນັກສຶກ</th>
                                <th>ຊື່ນັກສຶກ</th>
                                <th>ຫ້ອງ</th>
                                <th>ວັນທີ່ເຂົ້າ</th>
                                <th>ຈຳນວນ</th>
                                <th>ສະຖານະ</th>
                                <th>ອອກຫ້ອງ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                                $sql = "SELECT rm.*, s.Stu_name, r.R_number, r.Build
                                    FROM room_members rm
                                    JOIN student s ON rm.Stu_id = s.Stu_ID
                                    JOIN room r ON rm.room_id = r.Room_ID
                                    ORDER BY rm.is_active DESC, rm.start_date DESC";
                                $result = mysqli_query($link, $sql);
                                $number = 1;
                                while ($row = mysqli_fetch_assoc($result)) {
                                ?>
                                <tr>
                                    <td class="text-center"><?php echo $number++ ?></td>
                                    <td class="text-center"><?php echo $row['Stu_id'] ?></td>
                                    <td><?php echo $row['Stu_name'] ?></td>
                                  <td>ຕຶກ <?php echo $row['Build'] ?> - ຫ້ອງ<?php echo $row['R_number'] ?></td>
                                    <td><?php echo $row['start_date'] ?></td>
                                    <td><?php echo $row['qty'] ?> ປີ</td>
                                    <td><?php echo $row['is_active'] ? 'ກຳລັງພັກຢູ່' : 'ອອກແລ້ວ' ?></td>
                                    <td class="text-center">
                                        <?php if ($row['is_active']) {?>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="id" value="<?php echo $row['id'] ?>">
                                                <input type="hidden" name="Stu_id" value="<?php echo $row['Stu_id'] ?>">
                                                <input type="hidden" name="room_id" value="<?php echo $row['room_id'] ?>">
                                                <button type="submit" name="btnDelete" class="btn btn-danger btn-sm"
                                                    onclick="return confirm('ທ່ານຕ້ອງການອອກຫ້ອງ?')">
                                                    ອອກຫ້ອງ
                                                </button>
                                            </form>
                                        <?php }?>
                                    </td>
                                </tr>
                            <?php }?>
                        </tbody>
                    </table>

                </fieldset>
            </div>
        </div>

    </div>

    <?php
        if (isset($message)) {
            echo $message;
        }
    ?>

    <?php include 'footer.php'; ?>
    </div>
</body>

<!--tom-select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script>
new TomSelect("#select-room", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫາໃບແຈ້ງໜີ້"
});
new TomSelect("#Stu_id", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກ"
});
</script>

</html>

