<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// สร้างเลขใบแจ้งหนี้อัตโนมัติ
function generateInvoiceNumber() {
    global $link;
    $datePart = date('Ymd');
    $prefix = "INV-" . $datePart;

    $sql = "SELECT MAX(invoice_number) AS invoice_number FROM invoices WHERE invoice_number LIKE '$prefix%'";
    $result = mysqli_query($link, $sql);
    $row = mysqli_fetch_assoc($result);

    if (empty($row['invoice_number'])) {
        $invoice_number = $prefix . "-001";
    } else {
        $lastId = substr($row['invoice_number'], -3);
        $newId = str_pad((int)$lastId + 1, 3, "0", STR_PAD_LEFT);
        $invoice_number = $prefix . "-" . $newId;
    }
    return $invoice_number;
}

// เมื่อกดปุ่มสร้างใบแจ้งหนี้
if (isset($_POST['btnCreate'])) {
    $Stu_id = data_input($_POST['Stu_id']);
    $room_id = data_input($_POST['room_id']);
    $invoice_date = data_input($_POST['invoice_date']);
    $due_date = data_input($_POST['due_date']);
    $period = data_input($_POST['period']);
    $note = data_input($_POST['note']);

    // ดึงราคาห้อง
    $sql_room = "SELECT Price FROM room WHERE Room_ID = ?";
    $stmt_room = mysqli_prepare($link, $sql_room);
    mysqli_stmt_bind_param($stmt_room, "s", $room_id);
    mysqli_stmt_execute($stmt_room);
    $result_room = mysqli_stmt_get_result($stmt_room);
    $room_data = mysqli_fetch_assoc($result_room);
    
    if (!$room_data) {
        $error_message = "ບໍ່ພົບຂໍ້ມູນຫ້ອງ";
    } else {
        $room_price = $room_data['Price'];
        $amount = $room_price * $period;

        // ตรวจสอบว่ามีใบแจ้งหนี้ซ้ำหรือไม่
        $sql_check = "SELECT COUNT(*) as count FROM invoices WHERE Stu_id = ? AND room_id = ? AND period = ? AND YEAR(invoice_date) = YEAR(?)";
        $stmt_check = mysqli_prepare($link, $sql_check);
        mysqli_stmt_bind_param($stmt_check, "ssss", $Stu_id, $room_id, $period, $invoice_date);
        mysqli_stmt_execute($stmt_check);
        $result_check = mysqli_stmt_get_result($stmt_check);
        $check_data = mysqli_fetch_assoc($result_check);

        if ($check_data['count'] > 0) {
            $error_message = "ມີໃບແຈ້ງໜີ້ງວດນີ້ແລ້ວ";
        } else {
            mysqli_begin_transaction($link);

            try {
                $invoice_number = generateInvoiceNumber();
                $status = 'unpaid';

                $sql_invoice = "INSERT INTO invoices (Stu_id, room_id, invoice_number, invoice_date, due_date, amount, status, period, note) 
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($link, $sql_invoice);
                
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . mysqli_error($link));
                }

                $bindResult = mysqli_stmt_bind_param($stmt, "sssssdsss", $Stu_id, $room_id, $invoice_number, $invoice_date, $due_date, $amount, $status, $period, $note);
                if (!$bindResult) {
                    throw new Exception("Bind failed: " . mysqli_stmt_error($stmt));
                }

                $execResult = mysqli_stmt_execute($stmt);
                if (!$execResult) {
                    throw new Exception("Execute failed: " . mysqli_stmt_error($stmt));
                }

                $invoice_id = mysqli_insert_id($link);
                mysqli_commit($link);

                $message = '<script>
                    swal("ສຳເລັດ", "ສ້າງໃບແຈ້ງໜີ້ສຳເລັດ", "success", {button: "ຕົກລົງ"}).then(function() {
                        window.location = "invoice/invoice-print.php?id=' . $invoice_id . '";
                    });
                </script>';

            } catch (Exception $e) {
                mysqli_rollback($link);
                $error_message = "ຜິດພາດ: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ລະບົບຈັດການຫໍພັກ - ສ້າງໃບແຈ້ງໜີ້</title>

    <link rel="icon" href="../images/3.png">

    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>
    <script src="../js/sweetalert.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <!-- Tom Select -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet">
    <style>
    .ts-dropdown .ts-dropdown-content {
        max-height: 200px;
        overflow-y: auto;
    }
    </style>
</head>

<body class="sb-nav-fixed">
    <?php include_once 'menu.php' ?>
    <div id="layoutSidenav_content">
        <div class="container-fluid mt-2">
            <div class="row">
                <div class="col-md-6 mt-4">
                    <fieldset class="border border-primary p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">ສ້າງໃບແຈ້ງໜີ້</legend>
                        
                        <?php if (isset($error_message)) { ?>
                            <div class="alert alert-danger"><?= $error_message ?></div>
                        <?php } ?>

                        <form method="post" enctype="multipart/form-data">
                            <div class="form-group mb-3">
                                <label for="Stu_id">ນັກສຶກສາ</label>
                                <select name="Stu_id" id="select-student" class="form-select" required>
                                    <option value="">--ເລຶອກນັກສຶກສາ--</option>
                                    <?php
                                    $sql = "SELECT rm.Stu_id, s.Stu_name, r.R_number, r.Build, r.Room_ID
                                            FROM room_members rm
                                            JOIN student s ON rm.Stu_id = s.Stu_ID
                                            JOIN room r ON rm.room_id = r.Room_ID
                                            WHERE rm.status = 'active'
                                            ORDER BY s.Stu_name ASC";
                                    $result = mysqli_query($link, $sql);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        $selected = (isset($_POST['Stu_id']) && $_POST['Stu_id'] == $row['Stu_id']) ? 'selected' : '';
                                        echo "<option value='{$row['Stu_id']}' data-room='{$row['Room_ID']}' {$selected}>
                                            {$row['Stu_name']} - ຕຶກ {$row['Build']} ຫ້ອງ {$row['R_number']}
                                        </option>";
                                    }
                                    ?>
                                </select>
                            </div>

                            <input type="hidden" name="room_id" id="room_id" value="<?= @$_POST['room_id'] ?>">

                            <div class="form-group mb-3">
                                <label for="invoice_date">ວັນທີອອກໃບແຈ້ງໜີ້</label>
                                <input type="date" name="invoice_date" id="invoice_date" class="form-control" 
                                       value="<?= isset($_POST['invoice_date']) ? $_POST['invoice_date'] : date('Y-m-d') ?>" required>
                            </div>

                            <div class="form-group mb-3">
                                <label for="due_date">ວັນທີຄົບກຳໜົດ</label>
                                <input type="date" name="due_date" id="due_date" class="form-control" 
                                       value="<?= isset($_POST['due_date']) ? $_POST['due_date'] : date('Y-m-d', strtotime('+7 days')) ?>" required>
                            </div>

                            <div class="form-group mb-3">
                                <label for="period">ງວດທີ</label>
                                <select name="period" id="period" class="form-select" required>
                                    <option value="">--ເລຶອກງວດ--</option>
                                    <option value="1" <?= (isset($_POST['period']) && $_POST['period'] == '1') ? 'selected' : '' ?>>ງວດທີ 1 (ມັງກອນ-ເມສາ)</option>
                                    <option value="2" <?= (isset($_POST['period']) && $_POST['period'] == '2') ? 'selected' : '' ?>>ງວດທີ 2 (ພຶດສະພາ-ສິງຫາ)</option>
                                    <option value="3" <?= (isset($_POST['period']) && $_POST['period'] == '3') ? 'selected' : '' ?>>ງວດທີ 3 (ກັນຍາ-ທັນວາ)</option>
                                </select>
                            </div>

                            <div class="form-group mb-3">
                                <label for="note">ໝາຍເຫດ</label>
                                <textarea name="note" id="note" class="form-control" rows="3" placeholder="ໝາຍເຫດເພີ່ມເຕີມ..."><?= @$_POST['note'] ?></textarea>
                            </div>

                            <button type="submit" name="btnCreate" class="btn btn-primary btn-block" style="margin-top: 20px;">
                                <i class="fas fa-plus"></i> ສ້າງໃບແຈ້ງໜີ້
                            </button>
                        </form>
                    </fieldset>
                </div>

                <div class="col-md-6 mt-4">
                    <fieldset class="border border-info p-2 px-4 pb-4" style="border-radius: 15px;">
                        <legend class="float-none w-auto p-2 h5">ໃບແຈ້ງໜີ້ລ່າສຸດ</legend>
                        <table id="example" class="table table-striped" style="width:100%">
                            <thead class="bg-secondary text-center text-white">
                                <tr>
                                    <th>ເລກທີ</th>
                                    <th>ນັກສຶກສາ</th>
                                    <th>ຫ້ອງ</th>
                                    <th>ຈຳນວນເງິນ</th>
                                    <th>ສະຖານະ</th>
                                    <th>ຈັດການ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sql = "SELECT i.*, s.Stu_name, r.R_number, r.Build
                                        FROM invoices i
                                        JOIN student s ON i.Stu_id = s.Stu_ID
                                        JOIN room r ON i.room_id = r.Room_ID
                                        ORDER BY i.invoice_date DESC
                                        LIMIT 10";
                                $result = mysqli_query($link, $sql);
                                while ($row = mysqli_fetch_assoc($result)) {
                                ?>
                                <tr>
                                    <td><?= $row['invoice_number'] ?></td>
                                    <td><?= $row['Stu_name'] ?></td>
                                    <td>ຕຶກ <?= $row['Build'] ?> ຫ້ອງ <?= $row['R_number'] ?></td>
                                    <td class="text-right"><?= number_format($row['amount'], 2) ?> ກີບ</td>
                                    <td class="text-center">
                                        <?php if ($row['status'] == 'paid') { ?>
                                            <span class="badge bg-success">ຊຳລະແລ້ວ</span>
                                        <?php } else { ?>
                                            <span class="badge bg-warning">ຍັງບໍ່ຊຳລະ</span>
                                        <?php } ?>
                                    </td>
                                    <td class="text-center">
                                        <a href="invoice/invoice-print.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </fieldset>
                </div>
            </div>
        </div>

        <?php
        if (isset($message)) {
            echo $message;
        }
        ?>

        <?php include_once 'footer.php' ?>
    </div>
</body>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#example').DataTable({
        "language": {
            "lengthMenu": "ສະແດງ _MENU_ ລາຍການ",
            "zeroRecords": "ບໍ່ພົບຂໍ້ມູນ",
            "info": "ສະແດງ _START_ ເຖິງ _END_ ຈາກ _TOTAL_ ລາຍການ",
            "infoEmpty": "ສະແດງ 0 ເຖິງ 0 ຈາກ 0 ລາຍການ",
            "infoFiltered": "(ກັ່ນຕອງຈາກ _MAX_ ລາຍການທັງໝົດ)",
            "search": "ຄົ້ນຫາ:",
            "paginate": {
                "first": "ໜ້າທຳອິດ",
                "last": "ໜ້າສຸດທ້າຍ",
                "next": "ໜ້າຕໍ່ໄປ",
                "previous": "ໜ້າກ່ອນ"
            }
        }
    });

    // Auto-fill room_id when student is selected
    $('#select-student').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var roomId = selectedOption.data('room');
        $('#room_id').val(roomId);
    });
});
</script>

<!--Tom Select-->
<script src="https://cdn.jsdelivr.net/npm/tom-select/dist/js/tom-select.complete.min.js"></script>
<script>
new TomSelect("#select-student", {
    create: false,
    sortField: {
        field: "text",
        direction: "asc"
    },
    placeholder: "ເລືອກ ຫຼື ຄົ້ນຫານັກສຶກສາ"
});
</script>

</html>
