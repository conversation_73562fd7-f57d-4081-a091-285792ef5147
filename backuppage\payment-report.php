<?php
include_once 'login-check.php';
require_once '../function/function.php';
require_once 'connect-db.php';

// กำหนดค่าเริ่มต้นสำหรับการกรอง
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // วันแรกของเดือน
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // วันสุดท้ายของเดือน
$payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : 'all';
$building = isset($_GET['building']) ? $_GET['building'] : 'all';

// สร้าง SQL query พื้นฐาน
$sql = "SELECT p.*, i.Stu_id, i.room_id, i.invoice_number, i.period, s.Stu_name, s.tell, r.R_number, r.Build, r.Price, rc.receipt_number
        FROM payments p
        JOIN invoices i ON p.invoice_id = i.id
        JOIN student s ON i.Stu_id = s.Stu_ID
        JOIN room r ON i.room_id = r.Room_ID
        JOIN receipts rc ON p.id = rc.payment_id
        WHERE p.paid_at BETWEEN '$start_date 00:00:00' AND '$end_date 23:59:59'";

// เพิ่มเงื่อนไขการกรอง
if ($payment_method != 'all') {
    $sql .= " AND p.payment_method = '$payment_method'";
}

if ($building != 'all') {
    $sql .= " AND r.Build = '$building'";
}

$sql .= " ORDER BY p.paid_at DESC";

$result = mysqli_query($link, $sql);

// คำนวณสรุปยอด
$total_amount = 0;
$total_cash = 0;
$total_transfer = 0;

$temp_result = mysqli_query($link, $sql);
while ($row = mysqli_fetch_assoc($temp_result)) {
    $total_amount += $row['amount_paid'];
    
    if ($row['payment_method'] == 'cash') {
        $total_cash += $row['amount_paid'];
    } else {
        $total_transfer += $row['amount_paid'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>ລາຍງານການຊຳລະເງິນ</title>
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <link href="../../css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    <style>
        @media print {
            .no-print {
                display: none;
            }
            .table-responsive {
                overflow: visible !important;
            }
        }
        .summary-box {
            border-radius: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            margin-bottom: 20px;
        }
        .summary-title {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .summary-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .cash {
            color: #28a745;
        }
        .transfer {
            color: #007bff;
        }
    </style>
</head>

<body class="sb-nav-fixed">
    <?php include 'menu.php'; ?>
    <div id="layoutSidenav_content">
        <main>
            <div class="container-fluid px-4">
                <h1 class="mt-4">ລາຍງານການຊຳລະເງິນ</h1>
                <ol class="breadcrumb mb-4">
                    <li class="breadcrumb-item"><a href="../index.php">ໜ້າຫຼັກ</a></li>
                    <li class="breadcrumb-item active">ລາຍງານການຊຳລະເງິນ</li>
                </ol>

                <!-- ຟອມຄົ້ນຫາ -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <i class="fas fa-filter me-1"></i>
                        ຕົວກອງຂໍ້ມູນ
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">ວັນທີເລີ່ມຕົ້ນ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">ວັນທີສິ້ນສຸດ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="payment_method" class="form-label">ວິທີຊຳລະ</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="all" <?php echo $payment_method == 'all' ? 'selected' : ''; ?>>ທັງໝົດ</option>
                                    <option value="cash" <?php echo $payment_method == 'cash' ? 'selected' : ''; ?>>ເງິນສົດ</option>
                                    <option value="bank_transfer" <?php echo $payment_method == 'bank_transfer' ? 'selected' : ''; ?>>ໂອນເງິນ</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="building" class="form-label">ຕຶກ</label>
                                <select class="form-select" id="building" name="building">
                                    <option value="all" <?php echo $building == 'all' ? 'selected' : ''; ?>>ທັງໝົດ</option>
                                    <?php
                                    $sql_building = "SELECT DISTINCT Build FROM room ORDER BY Build";
                                    $result_building = mysqli_query($link, $sql_building);
                                    while ($row_building = mysqli_fetch_assoc($result_building)) {
                                        $selected = ($building == $row_building['Build']) ? 'selected' : '';
                                        echo "<option value='{$row_building['Build']}' $selected>ຕຶກ {$row_building['Build']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> ຄົ້ນຫາ
                                </button>
                                <button type="button" class="btn btn-success" onclick="window.print()">
                                    <i class="fas fa-print"></i> ພິມ
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- ສະຫຼຸບຂໍ້ມູນ -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="summary-box">
                            <div class="summary-title">ຍອດຊຳລະທັງໝົດ</div>
                            <div class="summary-value"><?= number_format($total_amount, 2) ?> ກີບ</div>
                            <div>ຈຳນວນ <?= mysqli_num_rows($temp_result) ?> ລາຍການ</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="summary-box">
                            <div class="summary-title">ຊຳລະດ້ວຍເງິນສົດ</div>
                            <div class="summary-value cash"><?= number_format($total_cash, 2) ?> ກີບ</div>
                            <div>ຄິດເປັນ <?= $total_amount > 0 ? number_format(($total_cash / $total_amount) * 100, 2) : 0 ?>%</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="summary-box">
                            <div class="summary-title">ຊຳລະດ້ວຍການໂອນ</div>
                            <div class="summary-value transfer"><?= number_format($total_transfer, 2) ?> ກີບ</div>
                            <div>ຄິດເປັນ <?= $total_amount > 0 ? number_format(($total_transfer / $total_amount) * 100, 2) : 0 ?>%</div>
                        </div>
                    </div>
                </div>

                <!-- ຕາຕະລາງຂໍ້ມູນ -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-table me-1"></i>
                        ລາຍງານການຊຳລະເງິນ (<?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?>)
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="dataTable" width="100%" cellspacing="0">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th>ລຳດັບ</th>
                                        <th>ວັນທີຊຳລະ</th>
                                        <th>ເລກທີໃບເສັດ</th>
                                        <th>ເລກທີໃບແຈ້ງໜີ້</th>
                                        <th>ນັກສຶກສາ</th>
                                        <th>ຫ້ອງພັກ</th>
                                        <th>ງວດທີ</th>
                                        <th>ຈຳນວນເງິນ</th>
                                        <th>ວິທີຊຳລະ</th>
                                        <th class="no-print">ຈັດການ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $i = 1;
                                    mysqli_data_seek($result, 0);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                    ?>
                                        <tr>
                                            <td><?php echo $i++; ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($row['paid_at'])); ?></td>
                                            <td><?php echo $row['receipt_number']; ?></td>
                                            <td><?php echo $row['invoice_number']; ?></td>
                                            <td><?php echo $row['Stu_name']; ?></td>
                                            <td>ຕຶກ <?php echo $row['Build']; ?> ຫ້ອງ <?php echo $row['R_number']; ?></td>
                                            <td><?php echo $row['period']; ?></td>
                                            <td class="text-end"><?php echo number_format($row['amount_paid'], 2); ?> ກີບ</td>
                                            <td>
                                                <?php if ($row['payment_method'] == 'cash') : ?>
                                                    <span class="badge bg-success">ເງິນສົດ</span>
                                                <?php else : ?>
                                                    <span class="badge bg-primary">ໂອນເງິນ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="no-print text-center">
                                                <a href="../receipt-print.php?id=<?php echo $row['id']; ?>" class="btn btn-info btn-sm" target="_blank">
                                                    <i class="fas fa-print"></i> ພິມບິນ
                                                </a>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light">
                                        <th colspan="7" class="text-end">ລວມທັງໝົດ:</th>
                                        <th class="text-end"><?php echo number_format($total_amount, 2); ?> ກີບ</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <footer class="py-4 bg-light mt-auto">
            <div class="container-fluid px-4">
                <div class="d-flex align-items-center justify-content-between small">
                    <div class="text-muted">ລະບົບຈັດການຫໍພັກ &copy; 2023</div>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js" crossorigin="anonymous"></script>
    <script>
        // Initialize DataTable
        window.addEventListener('DOMContentLoaded', event => {
            const datatablesSimple = document.getElementById('dataTable');
            if (datatablesSimple) {
                new simpleDatatables.DataTable(datatablesSimple, {
                    labels: {
                        placeholder: "ຄົ້ນຫາ...",
                        perPage: "ສະແດງລາຍການ",
                        noRows: "ບໍ່ພົບຂໍ້ມູນ",
                        info: "ສະແດງ {start} ຫາ {end} ຈາກ {rows} ລາຍການ",
                    }
                });
            }
        });
    </script>
</body>
</html>