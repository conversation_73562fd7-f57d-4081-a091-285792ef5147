<?php
include_once 'login-check.php'; 
require_once '../function/function.php';
require_once 'connect-db.php';

// // ตรวจสอบว่ามีการส่ง ID มาหรือไม่
if (!isset($_GET['id'])) {
    echo "<script>window.location = 'payment.php';</script>";
    exit;
}

$receipt_id = data_input($_GET['id']);

// ดึงข้อมูลใบเสร็จ
$sql = "SELECT r.*, p.paid_at, p.amount_paid, p.payment_method, p.invoice_id,
        i.Stu_id, i.room_id, i.invoice_date, i.period,
        s.Stu_name, s.tell,s.gender,
        rm.R_number, rm.Build, rm.Price
        FROM receipts r
        JOIN payments p ON r.payment_id = p.id
        JOIN invoices i ON p.invoice_id = i.id
        JOIN student s ON i.Stu_id = s.Stu_ID
        JOIN room rm ON i.room_id = rm.Room_ID
        WHERE r.id = '$receipt_id'";
$result = mysqli_query($link, $sql);

if (mysqli_num_rows($result) == 0) {
    echo "<script>alert('ไม่พบข้อมูลใบเสร็จ'); window.location = 'payment.php';</script>";
    exit;
}

$row = mysqli_fetch_assoc($result);
$gender = $row['gender'] == 'ຊ' ? 'ທ້າວ' : 'ນາງ';

// ดึงข้อมูลหอพัก
$sql_dorm = "SELECT * FROM room WHERE Room_ID = 1";
$result_dorm = mysqli_query($link, $sql_dorm);
$dorm = mysqli_fetch_assoc($result_dorm);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ໃບບິນຮັບເງິນ <?= $row['receipt_number'] ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
    body {
        font-family: 'Sarabun', sans-serif;
        font-size: 14px;
    }

    .receipt-header {
        text-align: center;
        margin-bottom: 20px;
    }

    .receipt-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .receipt-number {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .receipt-date {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .receipt-info {
        margin-bottom: 20px;
    }

    .receipt-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .receipt-table th,
    .receipt-table td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    .receipt-table th {
        background-color: #f2f2f2;
        text-align: center;
    }

    .receipt-footer {
        margin-top: 40px;
        text-align: center;
    }

    .signature {
        margin-top: 60px;
    }

    @media print {
        .no-print {
            display: none;
        }

        @page {
            size: A4;
            margin: 10mm;
        }
    }
    </style>
</head>

<body>
    <div class="container mt-4">
        <div class="row no-print mb-3">
            <div class="col-12">
                <button onclick="window.print()" class="btn btn-primary">ພິມໃບບິນ</button>
                <a href="payment.php" class="btn btn-secondary">ກັບຄືນ</a>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="receipt-header" style="display: flex; align-items: center;">
                    <div><img src="../images/3.png" width="100px" alt="ຮູບກາຊາດ">
                    </div>
                    <div style="margin-left: 300px;">
                        <h1 class="receipt-title">ໃບບິນຮັບເງິນ</h1>
                        <div class="receipt-number">ເລກທີ່ບິນ: <?= $row['receipt_number'] ?></div>
                        <div class="receipt-date">ວັນທີ່ອອກບິນ: <?= date('d/m/Y H:i', strtotime($row['issued_at'])) ?>
                        </div>
                    </div>

                </div>

                <div class="row receipt-info">
                    <div class="col-4">
                        <strong>ຂໍ້ມູນຫໍພັກ:</strong><br>
                        <!-- <p>ຫ້ອງພັກ: ຕຶກ <?= $row['Build'] ?> ຫ້ອງເບີ <?= $row['R_number'] ?> <br> -->
                            ບ້ານ ຄຳຮຸ່ງ ຮ່ອມ 5 ມ.ໄຊທານີ <br>
                            ໂທ: 020 5555 6666 <br>
                            facebook: @dormitory</p>
                    </div>
                    <div class="col-4"></div>
                    <div class="col-4 text-start">
                        <strong>ຂໍ້ມູນຜູ້ຊຳລະ:</strong><br>
                        ຊື່-ນາມສະກຸນ: <?= $gender ." ". $row['Stu_name'] ?><br>
                        ລະຫັດນັກສຶກສາ: <?= $row['Stu_id'] ?><br>
                        ເບີໂທ: <?= $row['tell'] ?>
                    </div>
                </div>

                <div class="row receipt-info">
                    <div class="col-12">
                        <strong>ລາຍລະອຽດ:</strong><br>
                        ຫ້ອງພັກ: ຕຶກ <?= $row['Build'] ?> ຫ້ອງເບີ <?= $row['R_number'] ?><br>
                        ງວດທີ່: <?= $row['period'] ?><br>
                        ວັນທີ່ຊຳລະ: <?= date('d/m/Y H:i', strtotime($row['paid_at'])) ?><br>
                        ວິທີຊຳລະ: <?= $row['payment_method'] == 'cash' ? 'ເງິນສົດ' : 'ໂອນເງິນ' ?>
                    </div>
                </div>

                <table class="receipt-table">
                    <thead>
                        <tr>
                            <th width="10%">ລຳດັບ</th>
                            <th width="50%">ລາຍການ</th>
                            <th width="20%">ຈຳນວນເງິນ</th>
                            <th width="20%">ໝາຍເຫດ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="text-center">1</td>
                            <td>ຄ່າເຊົ່າຫ້ອງພັກຫ້ອງ ຕຶກ <?= $row['Build'] ?> ຫ້ອງເບີ <?= $row['R_number'] ?> ງວດທີ່
                                <?= $row['period'] ?></td>
                            <td class="text-right"><?= number_format($row['Price'], 2) ?></td>
                            <td></td>
                        </tr>
                        <?php if ($row['amount_paid'] > $row['Price']) { ?>
                        <tr>
                            <td class="text-center">2</td>
                            <td>ຄ່ານ້ຳ-ຄ່າລາຄາ ແລະ ຄ່າບໍລິການອື່ນໆ</td>
                            <td class="text-right"><?= number_format($row['amount_paid'] - $row['Price'], 2) ?></td>
                            <td></td>
                        </tr>
                        <?php } ?>
                        <tr>
                            <td colspan="2" class="text-right"><strong>ລວມທັງໝົດ</strong></td>
                            <td class="text-right"><strong><?= number_format($row['amount_paid'], 2) ?></strong></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>

                <div class="row receipt-footer">
                    <div class="col-4 text-center">
                        <div class="signature">
                            ____________________________<br>
                            (ທ່ານ ຄຳແຜ່ນ ຄຳໂຄສີ)<br>
                            ຜູ້ອຳນວຍການ
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="signature">
                            ____________________________<br>
                            (<?=$gender ." ". $row['Stu_name'] ?>)<br>
                            ຜູ້ຊຳລະ
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="signature">
                            ____________________________<br>
                            (<?= $_SESSION['name'] ?>)<br>
                            ຜູ້ຮັບເງິນ
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <small>ເອກະສານນີ້ເປັນຫຼັກຖານການຊໍ່າລະເງີນ ກາລຸນາເກັບຮັກສາໄວ້ເປັນຫຼັກຖານ</small>
                </div>
            </div>
        </div>
    </div>
</body>

</html>