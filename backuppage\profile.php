<?php
    session_start();
    include_once 'connect-db.php';

    if (! isset($_SESSION['user_id'])) {
        header("Location: login-form.php");
        exit;
    }

    $user_id = $_SESSION['user_id'];

    // Fetch user data
    $sql  = "SELECT * FROM users WHERE user_id = ?";
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user   = $result->fetch_assoc();



    // Get role display name
    $role_display = [
        'admin'    => 'ຜູ້ດູແລລະບົບ',
        'employee' => 'ພະນັກງານ',
        'customer' => 'ລູກຄ້າ',
        'user'     => 'ຜູ້ໃຊ້ທົ່ວໄປ',
    ][$user['role']] ?? $user['role'];


    // Calculate account age
    $created_date = new DateTime($user['created_at'] ?? date('Y-m-d'));
    $now          = new DateTime();
    $account_age  = $created_date->diff($now)->days;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />

    <title>ລະບົບຈັດການຂໍ້ມູນພະນັກງານ</title>
    <link rel="icon" href="images/icon_logo.jpg">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/mystyle.css">
    <link rel="stylesheet" href="../fontawesome/css/all.min.css">

    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="../js/scripts.js"></script>

    <script src="../js/sweetalert.min.js"></script>
    <!-- datatable -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/jquery.dataTables.min.js"></script>
    <script src="../js/dataTables.bootstrap5.min.js"></script>

    <script src="../js/jquery.priceformat.min.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .profile-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .profile-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .profile-card .card-header {
            background-color: #4e73df;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .btn-icon {
            margin-right: 5px;
        }
        .booking-item {
            border-left: 4px solid #4e73df;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        .booking-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .account-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
     <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <link href="/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
</head>

<body class="sb-nav-fixed">
    <!-- ດຶງເມນູເຂົ້າມາ  -->
   <?php if ($user['role'] == 'admin' || $user['role'] == 'employee'): ?>
<?php include_once 'menu.php'?>
        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid px-4 py-4">
    <?php else: ?>
        <div class="container py-5">
    <?php endif; ?>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success_message']?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error_message']?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

        <div class="row">
            <div class="col-lg-4">
                <div class="card profile-card">
                    <div class="card-body text-center position-relative">
                        <span class="badge bg-primary account-badge"><?php echo $role_display?></span>
                        <img src="https://ui-avatars.com/api/?name=<?php echo urlencode($user['name'])?>&background=random&color=fff&size=150" class="profile-img mb-3" alt="Profile Image">
                        <h4><?php echo htmlspecialchars($user['name'])?></h4>
                        <p class="text-muted">
                            <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($user['username'])?>
                        </p>
                        <?php if (! empty($user['tel'])): ?>
                            <p class="text-muted">
                                <i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($user['tel'])?>
                            </p>
                        <?php endif; ?>
                        <p class="small text-muted">ເປັນສະມາຊິກມາແລ້ວ <?php echo $account_age?> ວັນ</p>
                        <div class="mt-3">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                                <i class="fas fa-edit btn-icon"></i>ແກ້ໄຂຂໍ້ມູນ
                            </button>
                            <a href="change_password.php" class="btn btn-warning">
                                <i class="fas fa-key btn-icon"></i>ປ່ຽນລະຫັດຜ່ານ
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card profile-card">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-2"></i>ຂໍ້ມູນເພີ່ມເຕີມ
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <?php if (! empty($user['address'])): ?>
                                <li class="list-group-item">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                    <strong>ທີ່ຢູ່:</strong> <?php echo htmlspecialchars($user['address'])?>
                                </li>
                            <?php endif; ?>
                            <li class="list-group-item">
                                <i class="fas fa-user-shield me-2 text-primary"></i>
                                <strong>ສິດທິ:</strong> <?php echo $role_display?>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                <strong>ສະໝັກເມື່ອ:</strong> <?php echo date('d/m/Y', strtotime($user['created_at'] ?? 'now'))?>
                            </li>
                        </ul>
                    </div>
                </div>
                <?php if ($user['role'] == 'admin' || $user['role'] == 'employee'): {?>
	                <div class="mt-3">
	                    <a href="#" class="btn btn-secondary" onclick="window.location.href='../index.php'">
	                        <i class="fas fa-arrow-left btn-icon"></i>ຍ້ອນກັບ
	                    </a>
	                </div>
	                <?php } elseif ($user['role'] == 'user' || $user['role'] == 'customer'): {?>
	                <div class="mt-3">
	                    <a href="#" class="btn btn-primary" onclick="window.location.href='../index.php'">
	                        <i class="fas fa-arrow-left btn-icon"></i>ຍ້ອນກັບ
	                    </a>
	                </div>
	                <?php }endif; ?>
            </div>

            <div class="col-lg-8">
                <?php if ($user['role'] == 'customer' && ! empty($bookings)): ?>
                    <div class="card profile-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-calendar-check me-2"></i>ການຈອງຫຼ້າສຸດ
                            </div>
                            <a href="../booking/my_bookings.php" class="btn btn-sm btn-outline-light">ເບິ່ງທັງໝົດ</a>
                        </div>
                        <div class="card-body">
                            <?php foreach ($bookings as $booking): ?>
                                <div class="booking-item p-3 bg-light rounded">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6><?php echo htmlspecialchars($booking['court_name'])?></h6>
                                            <p class="mb-0 text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('d/m/Y', strtotime($booking['booking_date']))?> |
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('H:i', strtotime($booking['start_time']))?> -
                                                <?php echo date('H:i', strtotime($booking['end_time']))?>
                                            </p>
                                        </div>
                                        <div class="text-end">

                                            <p class="mb-0 mt-1"><strong><?php echo number_format($booking['total_price'])?> ກີບ</strong></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Additional content can be added here based on user role -->
                <?php if ($user['role'] == 'admin'): ?>
                    <div class="card profile-card">
                        <div class="card-header">
                            <i class="fas fa-tachometer-alt me-2"></i>ການຈັດການລະບົບ
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <a href="index.php" class="btn btn-primary w-100">
                                        <i class="fas fa-home btn-icon"></i>ໜ້າຫຼັກຜູ້ດູແລລະບົບ
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="emp-management.php" class="btn btn-info w-100">
                                        <i class="fas fa-user btn-icon"></i>ຈັດການພະນັກງານ
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="room.php" class="btn btn-success w-100">
                                        <i class="fas fa-bed btn-icon"></i>ຈັດການຂໍ້ມູນຫ້ອງ
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="student-management.php" class="btn btn-warning w-100">
                                        <i class="fas fa-users btn-icon"></i>ຈັດການນັກສຶກສາ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php if ($user['role'] == 'admin' || $user['role'] == 'employee'): ?>
                </div>
            </main>
        </div>
    <?php else: ?>
        </div>
    <?php endif; ?>

    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">ແກ້ໄຂຂໍ້ມູນສ່ວນຕົວ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">ຊື່</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($user['name'])?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">ອີເມວ</label>
                            <input type="email" class="form-control" id="email" value="<?php echo htmlspecialchars($user['username'])?>" readonly>
                            <div class="form-text text-muted">ບໍ່ສາມາດປ່ຽນອີເມວໄດ້</div>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">ເບີໂທ</label>
                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($user['tel'])?>">
                        </div>
                        <!-- <div class="mb-3">
                            <label for="address" class="form-label">ທີ່ຢູ່</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($user['address'])?></textarea>
                        </div> -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ຍົກເລີກ</button>
                        <button type="submit" name="update_profile" class="btn btn-primary">ບັນທຶກ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>
    <?php if ($user['role'] == 'admin' || $user['role'] == 'employee'): ?>
        <script src="../js/scripts.js"></script>
    <?php endif; ?>
</body>

</html>

<script>
    //ບໍ່ໃຫ້ຟອມ submit ຄ່າຄືນໃໝ່
    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }

    //ສະແດງຂໍ້ມູນໃນຕາຕະລາງ
    $(document).ready(function() {
        $('#example').DataTable();
    });
</script>